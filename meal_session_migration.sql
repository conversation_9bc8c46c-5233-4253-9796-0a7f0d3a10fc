-- Add meal session support to group foods scanned together
-- Run this in your Supabase SQL Editor

-- Add meal_session_id column to food_logs table
ALTER TABLE food_logs 
ADD COLUMN meal_session_id UUID DEFAULT gen_random_uuid();

-- Create index for faster meal session queries
CREATE INDEX idx_food_logs_meal_session ON food_logs(meal_session_id);
CREATE INDEX idx_food_logs_user_logged_at ON food_logs(user_id, logged_at DESC);

-- Update daily_summaries to count meal sessions instead of individual items
-- This gives a more accurate "meals logged" count
COMMENT ON COLUMN daily_summaries.meals_logged IS 'Number of meal sessions (not individual food items)';