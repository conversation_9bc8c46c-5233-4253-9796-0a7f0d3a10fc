-- DEV Performance Optimization Migration - Clean and Direct
-- Since you're the only user and app isn't live, this does a clean migration
-- Fixes the SQL syntax errors from the original

-- =============================================================================
-- PART 1: CRITICAL MISSING INDEXES (Main Fix)
-- =============================================================================

-- This is the PRIMARY fix for "Unindexed foreign keys" warning
CREATE INDEX IF NOT EXISTS idx_nutrition_data_food_log_id 
ON nutrition_data USING btree (food_log_id);

-- Performance indexes for your app's query patterns
CREATE INDEX IF NOT EXISTS idx_food_logs_user_id 
ON food_logs USING btree (user_id);

CREATE INDEX IF NOT EXISTS idx_food_logs_user_date 
ON food_logs USING btree (user_id, logged_at DESC);

CREATE INDEX IF NOT EXISTS idx_food_logs_meal_session 
ON food_logs USING btree (meal_session_id) 
WHERE meal_session_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_daily_summaries_user_id 
ON daily_summaries USING btree (user_id);

CREATE INDEX IF NOT EXISTS idx_daily_summaries_user_date 
ON daily_summaries USING btree (user_id, date DESC);

-- =============================================================================
-- PART 2: CLEAN RLS POLICY MIGRATION
-- =============================================================================

-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE food_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE nutrition_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_summaries ENABLE ROW LEVEL SECURITY;

-- Drop existing policies (clean slate since you're in dev)
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON profiles;
DROP POLICY IF EXISTS "Enable update for users based on id" ON profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;

DROP POLICY IF EXISTS "Enable read access for food logs" ON food_logs;
DROP POLICY IF EXISTS "Enable insert for food logs" ON food_logs;
DROP POLICY IF EXISTS "Enable update for food logs" ON food_logs;
DROP POLICY IF EXISTS "Enable delete for food logs" ON food_logs;
DROP POLICY IF EXISTS "Users can access own food logs" ON food_logs;
DROP POLICY IF EXISTS "authenticated_users_own_food_logs" ON food_logs;

DROP POLICY IF EXISTS "Enable read access for nutrition data" ON nutrition_data;
DROP POLICY IF EXISTS "Enable insert for nutrition data" ON nutrition_data;
DROP POLICY IF EXISTS "Enable update for nutrition data" ON nutrition_data;
DROP POLICY IF EXISTS "Enable delete for nutrition data" ON nutrition_data;
DROP POLICY IF EXISTS "Users can access nutrition data for their food logs" ON nutrition_data;

DROP POLICY IF EXISTS "Enable read access for daily summaries" ON daily_summaries;
DROP POLICY IF EXISTS "Enable insert for daily summaries" ON daily_summaries;
DROP POLICY IF EXISTS "Enable update for daily summaries" ON daily_summaries;
DROP POLICY IF EXISTS "Enable delete for daily summaries" ON daily_summaries;
DROP POLICY IF EXISTS "Users can access own daily summaries" ON daily_summaries;

-- Create optimized RLS policies (fixes the function re-evaluation issue)
CREATE POLICY "profiles_user_access" ON profiles
FOR ALL TO authenticated
USING (id = (SELECT auth.uid()))
WITH CHECK (id = (SELECT auth.uid()));

CREATE POLICY "food_logs_user_access" ON food_logs
FOR ALL TO authenticated
USING (user_id = (SELECT auth.uid()))
WITH CHECK (user_id = (SELECT auth.uid()));

CREATE POLICY "nutrition_data_user_access" ON nutrition_data
FOR ALL TO authenticated
USING (
  food_log_id IN (
    SELECT id FROM food_logs 
    WHERE user_id = (SELECT auth.uid())
  )
)
WITH CHECK (
  food_log_id IN (
    SELECT id FROM food_logs 
    WHERE user_id = (SELECT auth.uid())
  )
);

CREATE POLICY "daily_summaries_user_access" ON daily_summaries
FOR ALL TO authenticated
USING (user_id = (SELECT auth.uid()))
WITH CHECK (user_id = (SELECT auth.uid()));

-- =============================================================================
-- PART 3: PERFORMANCE FUNCTIONS (FIXED SYNTAX)
-- =============================================================================

-- Function to get user's recent food logs (FIXED the interval syntax)
CREATE OR REPLACE FUNCTION get_user_recent_food_logs(
  p_user_id UUID,
  p_days_back INTEGER DEFAULT 7
)
RETURNS TABLE(
  id UUID,
  user_id UUID,
  food_name TEXT,
  serving_size TEXT,
  quantity DECIMAL,
  meal_type TEXT,
  logged_at TIMESTAMP WITH TIME ZONE,
  meal_session_id UUID,
  calories INTEGER,
  protein DECIMAL,
  carbs DECIMAL,
  fat DECIMAL,
  fiber DECIMAL,
  sugar DECIMAL,
  sodium DECIMAL,
  cholesterol DECIMAL
)
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT 
    fl.id,
    fl.user_id,
    fl.food_name,
    fl.serving_size,
    fl.quantity,
    fl.meal_type,
    fl.logged_at,
    fl.meal_session_id,
    nd.calories,
    nd.protein,
    nd.carbs,
    nd.fat,
    nd.fiber,
    nd.sugar,
    nd.sodium,
    nd.cholesterol
  FROM food_logs fl
  JOIN nutrition_data nd ON fl.id = nd.food_log_id
  WHERE fl.user_id = p_user_id
    AND fl.logged_at >= (CURRENT_DATE - INTERVAL '1 day' * p_days_back)
  ORDER BY fl.logged_at DESC;
$$;

-- Function to calculate daily nutrition totals efficiently
CREATE OR REPLACE FUNCTION calculate_daily_nutrition(
  p_user_id UUID,
  p_date DATE
)
RETURNS TABLE(
  total_calories BIGINT,
  total_protein DECIMAL,
  total_carbs DECIMAL,
  total_fat DECIMAL,
  total_fiber DECIMAL,
  meal_count BIGINT
)
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT 
    COALESCE(SUM(nd.calories * fl.quantity), 0)::BIGINT as total_calories,
    COALESCE(SUM(nd.protein * fl.quantity), 0) as total_protein,
    COALESCE(SUM(nd.carbs * fl.quantity), 0) as total_carbs,
    COALESCE(SUM(nd.fat * fl.quantity), 0) as total_fat,
    COALESCE(SUM(nd.fiber * fl.quantity), 0) as total_fiber,
    COUNT(DISTINCT fl.meal_session_id) as meal_count
  FROM food_logs fl
  JOIN nutrition_data nd ON fl.id = nd.food_log_id
  WHERE fl.user_id = p_user_id
    AND fl.logged_at::date = p_date;
$$;

-- =============================================================================
-- VERIFICATION
-- =============================================================================

-- Quick verification that everything worked
SELECT 'Migration completed successfully! 🎉' as status;

-- Show what was created
SELECT 
  'Indexes Created' as item_type,
  COUNT(*) as count
FROM pg_indexes 
WHERE tablename IN ('food_logs', 'nutrition_data', 'profiles', 'daily_summaries')
  AND indexname LIKE 'idx_%'
UNION ALL
SELECT 
  'RLS Policies Created' as item_type,
  COUNT(*) as count
FROM pg_policies 
WHERE tablename IN ('food_logs', 'nutrition_data', 'profiles', 'daily_summaries')
  AND policyname LIKE '%_user_access'
UNION ALL
SELECT 
  'Performance Functions' as item_type,
  COUNT(*) as count
FROM pg_proc 
WHERE proname IN ('get_user_recent_food_logs', 'calculate_daily_nutrition');