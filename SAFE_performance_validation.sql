-- SAFE Performance Validation Script - MINIMAL TESTING ONLY
-- This script ONLY reads data and creates monitoring tools
-- NO DATA MODIFICATION - completely safe to run

-- =============================================================================
-- SAFETY CHECK: Verify we can read basic table information
-- =============================================================================

-- Check if tables exist (read-only)
SELECT 
  'Table Existence Check' as test_type,
  table_name,
  CASE WHEN table_name IS NOT NULL THEN 'EXISTS ✓' ELSE 'MISSING ✗' END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN ('profiles', 'food_logs', 'nutrition_data', 'daily_summaries')
ORDER BY table_name;

-- =============================================================================
-- INDEX VERIFICATION (Read-Only)
-- =============================================================================

-- List all indexes on your tables (completely safe)
SELECT 
  'Current Indexes' as info_type,
  schemaname,
  tablename,
  indexname,
  CASE 
    WHEN indexname LIKE 'idx_nutrition_data_food_log_id' THEN '🎯 KEY FIX'
    WHEN indexname LIKE 'idx_%' THEN '⚡ PERFORMANCE'
    ELSE '📋 EXISTING'
  END as index_type
FROM pg_indexes 
WHERE tablename IN ('food_logs', 'nutrition_data', 'profiles', 'daily_summaries')
  AND schemaname = 'public'
ORDER BY tablename, indexname;

-- =============================================================================
-- RLS POLICY STATUS (Read-Only)
-- =============================================================================

-- Check RLS is enabled (read-only check)
SELECT 
  'RLS Status' as check_type,
  schemaname,
  tablename,
  CASE 
    WHEN rowsecurity = true THEN 'ENABLED ✓'
    ELSE 'DISABLED ⚠️'
  END as rls_status
FROM pg_tables 
WHERE tablename IN ('food_logs', 'nutrition_data', 'profiles', 'daily_summaries')
  AND schemaname = 'public'
ORDER BY tablename;

-- Show current RLS policies (read-only)
SELECT 
  'Current RLS Policies' as info_type,
  tablename,
  policyname,
  cmd as operation,
  CASE 
    WHEN policyname LIKE 'optimized_%' THEN '🚀 NEW OPTIMIZED'
    ELSE '📋 EXISTING'
  END as policy_type
FROM pg_policies 
WHERE tablename IN ('food_logs', 'nutrition_data', 'profiles', 'daily_summaries')
ORDER BY tablename, policyname;

-- =============================================================================
-- TABLE SIZE AND STATISTICS (Read-Only)
-- =============================================================================

-- Show table sizes and row counts (informational only)
SELECT 
  'Table Statistics' as info_type,
  schemaname,
  tablename,
  n_tup_ins as total_inserts,
  n_tup_upd as total_updates,
  n_tup_del as total_deletes,
  n_live_tup as current_rows
FROM pg_stat_user_tables 
WHERE tablename IN ('food_logs', 'nutrition_data', 'profiles', 'daily_summaries')
ORDER BY tablename;

-- =============================================================================
-- SIMPLE CONNECTIVITY TEST (No Data Access)
-- =============================================================================

-- Just test that we can query system tables
SELECT 
  'Database Connection Test' as test_type,
  current_database() as database_name,
  current_user as current_user,
  'SUCCESS ✓' as status;

-- =============================================================================
-- FUNCTION EXISTENCE CHECK (Read-Only)
-- =============================================================================

-- Check if performance functions were created
SELECT 
  'Performance Functions' as info_type,
  proname as function_name,
  CASE 
    WHEN proname LIKE '%_optimized' THEN '🚀 NEW FUNCTION'
    WHEN proname IN ('get_user_recent_food_logs', 'calculate_daily_nutrition') THEN '⚡ PERFORMANCE'
    ELSE '📋 SYSTEM'
  END as function_type
FROM pg_proc 
WHERE proname IN (
  'get_user_recent_food_logs', 
  'calculate_daily_nutrition',
  'get_user_food_logs_optimized',
  'get_daily_nutrition_optimized'
)
ORDER BY proname;

-- =============================================================================
-- FINAL SAFE SUMMARY
-- =============================================================================

DO $$
DECLARE
  table_count INTEGER;
  index_count INTEGER;
  rls_enabled_count INTEGER;
  key_index_exists BOOLEAN;
BEGIN
  -- Count tables
  SELECT COUNT(*) INTO table_count
  FROM information_schema.tables 
  WHERE table_schema = 'public' 
    AND table_name IN ('profiles', 'food_logs', 'nutrition_data', 'daily_summaries');
  
  -- Count our performance indexes  
  SELECT COUNT(*) INTO index_count
  FROM pg_indexes 
  WHERE tablename IN ('food_logs', 'nutrition_data', 'profiles', 'daily_summaries')
    AND indexname LIKE 'idx_%';
    
  -- Count RLS-enabled tables
  SELECT COUNT(*) INTO rls_enabled_count
  FROM pg_tables 
  WHERE tablename IN ('food_logs', 'nutrition_data', 'profiles', 'daily_summaries')
    AND schemaname = 'public'
    AND rowsecurity = true;
    
  -- Check for the critical foreign key index
  SELECT EXISTS(
    SELECT 1 FROM pg_indexes 
    WHERE tablename = 'nutrition_data' 
    AND indexname = 'idx_nutrition_data_food_log_id'
  ) INTO key_index_exists;
  
  RAISE NOTICE '====================================';
  RAISE NOTICE '📊 SAFE VALIDATION SUMMARY';
  RAISE NOTICE '====================================';
  RAISE NOTICE 'Tables found: %/4', table_count;
  RAISE NOTICE 'Performance indexes: %', index_count;
  RAISE NOTICE 'RLS enabled tables: %/4', rls_enabled_count;
  RAISE NOTICE 'Critical foreign key index: %', 
    CASE WHEN key_index_exists THEN 'EXISTS ✓' ELSE 'MISSING ❌' END;
  RAISE NOTICE '';
  RAISE NOTICE '✅ This validation was completely safe';
  RAISE NOTICE '💡 No data was modified or accessed';
  RAISE NOTICE '🔍 Only system metadata was examined';
END $$;