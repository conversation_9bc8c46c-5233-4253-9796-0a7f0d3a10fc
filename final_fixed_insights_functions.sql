-- FINAL FIX: New Supabase RPC Functions for Enhanced Insights
-- Fixed INTERVAL syntax for PostgreSQL

-- 1. MEAL TIMING ANALYSIS (FINAL FIX)
CREATE OR REPLACE FUNCTION calculate_meal_timing_insights(
    p_user_id UUID,
    p_days_back INTEGER DEFAULT 7
)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    -- Calculate eating window and meal timing patterns
    WITH daily_meal_times AS (
        SELECT 
            DATE(fl.logged_at) as meal_date,
            MIN(EXTRACT(EPOCH FROM fl.logged_at::time) / 3600) as first_meal_hour,
            MAX(EXTRACT(EPOCH FROM fl.logged_at::time) / 3600) as last_meal_hour,
            COUNT(DISTINCT fl.meal_session_id) as daily_meals
        FROM food_logs fl
        WHERE fl.user_id = p_user_id 
        AND fl.logged_at >= CURRENT_DATE - (p_days_back || ' days')::INTERVAL
        GROUP BY DATE(fl.logged_at)
        HAVING COUNT(*) > 0
    ),
    averages AS (
        SELECT 
            AVG(last_meal_hour - first_meal_hour) as avg_eating_window,
            AVG(first_meal_hour) as avg_first_meal,
            AVG(last_meal_hour) as avg_last_meal,
            AVG(daily_meals) as avg_meals_per_day
        FROM daily_meal_times
    )
    SELECT json_build_object(
        'eating_window_hours', ROUND(COALESCE(avg_eating_window, 0)::numeric, 1),
        'avg_first_meal_hour', ROUND(COALESCE(avg_first_meal, 0)::numeric, 1),
        'avg_last_meal_hour', ROUND(COALESCE(avg_last_meal, 0)::numeric, 1),
        'avg_meals_per_day', ROUND(COALESCE(avg_meals_per_day, 0)::numeric, 1),
        'days_analyzed', (SELECT COUNT(DISTINCT DATE(logged_at)) FROM food_logs WHERE user_id = p_user_id AND logged_at >= CURRENT_DATE - (p_days_back || ' days')::INTERVAL)
    ) INTO result FROM averages;
    
    RETURN COALESCE(result, '{}'::json);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- 2. FOOD VARIETY SCORE (FINAL FIX)
CREATE OR REPLACE FUNCTION calculate_food_variety_score(
    p_user_id UUID,
    p_days_back INTEGER DEFAULT 7
)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    WITH food_variety AS (
        SELECT 
            COUNT(DISTINCT LOWER(TRIM(fl.food_name))) as unique_foods_count,
            COUNT(*) as total_food_entries,
            COUNT(DISTINCT DATE(fl.logged_at)) as days_with_data
        FROM food_logs fl
        WHERE fl.user_id = p_user_id 
        AND fl.logged_at >= CURRENT_DATE - (p_days_back || ' days')::INTERVAL
    ),
    daily_variety AS (
        SELECT 
            DATE(fl.logged_at) as log_date,
            COUNT(DISTINCT LOWER(TRIM(fl.food_name))) as daily_unique_foods
        FROM food_logs fl
        WHERE fl.user_id = p_user_id 
        AND fl.logged_at >= CURRENT_DATE - (p_days_back || ' days')::INTERVAL
        GROUP BY DATE(fl.logged_at)
    )
    SELECT json_build_object(
        'total_unique_foods', COALESCE(fv.unique_foods_count, 0),
        'avg_daily_variety', ROUND(COALESCE((SELECT AVG(daily_unique_foods) FROM daily_variety), 0)::numeric, 1),
        'variety_score', LEAST(100, ROUND(COALESCE(fv.unique_foods_count::numeric / GREATEST(p_days_back, 1) * 10, 0), 0)),
        'days_analyzed', COALESCE(fv.days_with_data, 0)
    ) INTO result 
    FROM food_variety fv;
    
    RETURN COALESCE(result, '{}'::json);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- 3. ENERGY DISTRIBUTION ANALYSIS (FINAL FIX)
CREATE OR REPLACE FUNCTION calculate_energy_distribution(
    p_user_id UUID,
    p_days_back INTEGER DEFAULT 7
)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    WITH hourly_calories AS (
        SELECT 
            EXTRACT(hour FROM fl.logged_at) as meal_hour,
            SUM(nd.calories * fl.quantity) as hour_calories
        FROM food_logs fl
        JOIN nutrition_data nd ON fl.id = nd.food_log_id
        WHERE fl.user_id = p_user_id 
        AND fl.logged_at >= CURRENT_DATE - (p_days_back || ' days')::INTERVAL
        GROUP BY EXTRACT(hour FROM fl.logged_at)
    ),
    time_periods AS (
        SELECT 
            SUM(CASE WHEN EXTRACT(hour FROM fl.logged_at) BETWEEN 6 AND 11 THEN nd.calories * fl.quantity ELSE 0 END) as morning_cals,
            SUM(CASE WHEN EXTRACT(hour FROM fl.logged_at) BETWEEN 12 AND 17 THEN nd.calories * fl.quantity ELSE 0 END) as afternoon_cals,
            SUM(CASE WHEN EXTRACT(hour FROM fl.logged_at) BETWEEN 18 AND 23 THEN nd.calories * fl.quantity ELSE 0 END) as evening_cals,
            SUM(nd.calories * fl.quantity) as total_period_cals
        FROM food_logs fl
        JOIN nutrition_data nd ON fl.id = nd.food_log_id
        WHERE fl.user_id = p_user_id 
        AND fl.logged_at >= CURRENT_DATE - (p_days_back || ' days')::INTERVAL
    )
    SELECT json_build_object(
        'morning_percentage', CASE WHEN COALESCE(tp.total_period_cals, 0) > 0 THEN ROUND((COALESCE(tp.morning_cals, 0) / tp.total_period_cals * 100)::numeric, 0) ELSE 0 END,
        'afternoon_percentage', CASE WHEN COALESCE(tp.total_period_cals, 0) > 0 THEN ROUND((COALESCE(tp.afternoon_cals, 0) / tp.total_period_cals * 100)::numeric, 0) ELSE 0 END,
        'evening_percentage', CASE WHEN COALESCE(tp.total_period_cals, 0) > 0 THEN ROUND((COALESCE(tp.evening_cals, 0) / tp.total_period_cals * 100)::numeric, 0) ELSE 0 END,
        'front_loaded', CASE WHEN COALESCE(tp.total_period_cals, 0) > 0 THEN (COALESCE(tp.morning_cals, 0) + COALESCE(tp.afternoon_cals, 0)) > COALESCE(tp.evening_cals, 0) ELSE false END,
        'peak_eating_hour', (SELECT meal_hour FROM hourly_calories ORDER BY hour_calories DESC LIMIT 1),
        'total_calories', COALESCE(tp.total_period_cals, 0)
    ) INTO result 
    FROM time_periods tp;
    
    RETURN COALESCE(result, '{}'::json);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION calculate_meal_timing_insights TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_food_variety_score TO authenticated;  
GRANT EXECUTE ON FUNCTION calculate_energy_distribution TO authenticated;