# Google Vision API Setup

## Current Status
- ✅ Google Vision API is integrated and ready
- ✅ App works with mock data (no API key required for testing)
- ✅ Real API integration is ready to activate

## To Enable Real Google Vision API:

### 1. Get Google Cloud Vision API Key
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the "Vision API"
4. Go to "Credentials" → "Create Credentials" → "API Key"
5. Copy your API key

### 2. Add API Key to Your App
In your app code, add this line during initialization:

```typescript
import { aiService } from '@/services/aiService';

// Add this in your app initialization (like in App.tsx)
aiService.setApiKey('YOUR_GOOGLE_VISION_API_KEY_HERE');
```

### 3. Security Best Practices
For production, use environment variables:

1. Create `.env` file:
```
GOOGLE_VISION_API_KEY=your_actual_api_key_here
```

2. Install expo-constants for env vars:
```bash
npm install expo-constants
```

3. Use in code:
```typescript
import Constants from 'expo-constants';

aiService.setApiKey(Constants.expoConfig?.extra?.googleVisionApiKey);
```

## How It Works

### Without API Key (Current - Mock Mode):
- Returns realistic fake food data
- Perfect for development and testing
- No API costs

### With API Key (Production Mode):
- Analyzes real photos using Google Vision
- Detects food items with confidence scores
- Falls back to mock data if detection fails
- Automatic nutrition data lookup

## API Features Used:
- **Label Detection**: Identifies general food categories
- **Object Localization**: Finds specific food items in images
- **Smart Filtering**: Only shows food-related items
- **Fallback Logic**: Graceful degradation to mock data

## Cost Information:
- Google Vision API: ~$1.50 per 1,000 requests
- Free tier: 1,000 requests/month
- Perfect for MVP and early users

Your app will work perfectly with mock data until you're ready to add the real API!