# Food Scanner Backend Setup Guide

## Overview
This guide covers implementing user accounts and database backend for the Food Scanner Mobile app using Supabase as the recommended solution.

## Why Supabase? (vs Firebase)

### Supabase ✅
- **PostgreSQL-based** - Perfect for complex nutrition data relationships
- **SQL queries** - Easy to query "foods with >20g protein" or nutrition analytics
- **Open source** - No vendor lock-in
- **Row-level security** - Automatic user data isolation
- **Built-in auth** - Email, Google, Apple sign-in included
- **Auto-generated APIs** - REST and GraphQL endpoints
- **Real-time** - Live data updates
- **Cost effective** - Predictable pricing based on storage

### Firebase (Alternative)
- **NoSQL** - Document-based, good for simple data
- **Google ecosystem** - Tight integration with Google services
- **Mature offline support** - Better offline capabilities
- **Rapid prototyping** - Faster initial setup

## Database Schema Design

### Tables Structure
```sql
-- Users (managed by <PERSON><PERSON><PERSON> Auth automatically)
users (
  id UUID PRIMARY KEY,
  email TEXT,
  created_at TIMESTAMP,
  -- Additional fields managed by <PERSON>pa<PERSON> Auth
)

-- User Profiles
profiles (
  id UUID REFERENCES users(id) PRIMARY KEY,
  username TEXT,
  full_name TEXT,
  avatar_url TEXT,
  daily_calorie_goal INTEGER DEFAULT 2000,
  daily_protein_goal INTEGER DEFAULT 150,
  daily_carb_goal INTEGER DEFAULT 250,
  daily_fat_goal INTEGER DEFAULT 65,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
)

-- Food Logs
food_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) NOT NULL,
  food_name TEXT NOT NULL,
  serving_size TEXT,
  portion_estimate TEXT,
  quantity DECIMAL DEFAULT 1.0,
  meal_type TEXT CHECK (meal_type IN ('breakfast', 'lunch', 'dinner', 'snack')),
  logged_at TIMESTAMP DEFAULT NOW(),
  created_at TIMESTAMP DEFAULT NOW()
)

-- Nutrition Data (normalized)
nutrition_data (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  food_log_id UUID REFERENCES food_logs(id) NOT NULL,
  calories INTEGER,
  protein DECIMAL,
  carbs DECIMAL,
  fat DECIMAL,
  fiber DECIMAL,
  sugar DECIMAL,
  sodium INTEGER,
  cholesterol INTEGER,
  confidence DECIMAL -- AI confidence score
)

-- Daily Summaries (for quick dashboard queries)
daily_summaries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) NOT NULL,
  date DATE NOT NULL,
  total_calories INTEGER DEFAULT 0,
  total_protein DECIMAL DEFAULT 0,
  total_carbs DECIMAL DEFAULT 0,
  total_fat DECIMAL DEFAULT 0,
  total_fiber DECIMAL DEFAULT 0,
  meals_logged INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, date)
)
```

### Row Level Security (RLS) Policies
```sql
-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE food_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE nutrition_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_summaries ENABLE ROW LEVEL SECURITY;

-- Profiles: Users can only see/edit their own profile
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- Food Logs: Users can only see/edit their own logs
CREATE POLICY "Users can view own food logs" ON food_logs FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own food logs" ON food_logs FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own food logs" ON food_logs FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own food logs" ON food_logs FOR DELETE USING (auth.uid() = user_id);

-- Nutrition Data: Users can only access nutrition data for their food logs
CREATE POLICY "Users can view nutrition data for own food logs" ON nutrition_data FOR SELECT 
USING (EXISTS (SELECT 1 FROM food_logs WHERE food_logs.id = nutrition_data.food_log_id AND food_logs.user_id = auth.uid()));

-- Daily Summaries: Users can only see their own summaries
CREATE POLICY "Users can view own daily summaries" ON daily_summaries FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own daily summaries" ON daily_summaries FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own daily summaries" ON daily_summaries FOR UPDATE USING (auth.uid() = user_id);
```

## Implementation Steps

### Phase 1: Supabase Setup
1. **Create Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create free account
   - Create new project
   - Note project URL and anon key

2. **Database Setup**
   - Run the SQL schema above in Supabase SQL Editor
   - Set up RLS policies
   - Test with sample data

3. **Environment Configuration**
   ```bash
   # Add to .env
   SUPABASE_URL=your_project_url_here
   SUPABASE_ANON_KEY=your_anon_key_here
   ```

### Phase 2: React Native Integration
1. **Install Dependencies**
   ```bash
   npm install @supabase/supabase-js
   npm install @react-native-async-storage/async-storage
   ```

2. **Create Supabase Client**
   ```javascript
   // src/services/supabase.js
   import { createClient } from '@supabase/supabase-js'
   import AsyncStorage from '@react-native-async-storage/async-storage'
   import { SUPABASE_URL, SUPABASE_ANON_KEY } from '@env'

   export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
     auth: {
       storage: AsyncStorage,
       autoRefreshToken: true,
       persistSession: true,
       detectSessionInUrl: false,
     },
   })
   ```

3. **Authentication Service**
   ```javascript
   // src/services/authService.js
   import { supabase } from './supabase'

   export const authService = {
     // Sign up
     async signUp(email, password) {
       const { data, error } = await supabase.auth.signUp({
         email,
         password,
       })
       return { data, error }
     },

     // Sign in
     async signIn(email, password) {
       const { data, error } = await supabase.auth.signInWithPassword({
         email,
         password,
       })
       return { data, error }
     },

     // Sign out
     async signOut() {
       const { error } = await supabase.auth.signOut()
       return { error }
     },

     // Get current user
     getCurrentUser() {
       return supabase.auth.getUser()
     },

     // Listen to auth changes
     onAuthStateChange(callback) {
       return supabase.auth.onAuthStateChange(callback)
     }
   }
   ```

4. **Food Logging Service**
   ```javascript
   // src/services/foodLogService.js
   import { supabase } from './supabase'

   export const foodLogService = {
     // Create food log entry
     async logFood(foodData, nutritionData) {
       // Insert food log
       const { data: foodLog, error: foodError } = await supabase
         .from('food_logs')
         .insert([foodData])
         .select()
         .single()

       if (foodError) return { error: foodError }

       // Insert nutrition data
       const { data: nutrition, error: nutritionError } = await supabase
         .from('nutrition_data')
         .insert([{ ...nutritionData, food_log_id: foodLog.id }])
         .select()
         .single()

       return { data: { foodLog, nutrition }, error: nutritionError }
     },

     // Get user's food logs
     async getFoodLogs(userId, date = null) {
       let query = supabase
         .from('food_logs')
         .select(`
           *,
           nutrition_data (*)
         `)
         .eq('user_id', userId)
         .order('logged_at', { ascending: false })

       if (date) {
         query = query.gte('logged_at', `${date}T00:00:00`)
                     .lte('logged_at', `${date}T23:59:59`)
       }

       const { data, error } = await query
       return { data, error }
     },

     // Get daily summary
     async getDailySummary(userId, date) {
       const { data, error } = await supabase
         .from('daily_summaries')
         .select('*')
         .eq('user_id', userId)
         .eq('date', date)
         .single()

       return { data, error }
     }
   }
   ```

### Phase 3: UI Integration
1. **Auth Context**
   ```javascript
   // src/contexts/AuthContext.js
   import React, { createContext, useContext, useEffect, useState } from 'react'
   import { authService } from '../services/authService'

   const AuthContext = createContext({})

   export const useAuth = () => useContext(AuthContext)

   export const AuthProvider = ({ children }) => {
     const [user, setUser] = useState(null)
     const [loading, setLoading] = useState(true)

     useEffect(() => {
       // Get initial session
       authService.getCurrentUser().then(({ data: { user } }) => {
         setUser(user)
         setLoading(false)
       })

       // Listen for auth changes
       const { data: { subscription } } = authService.onAuthStateChange(
         (event, session) => {
           setUser(session?.user ?? null)
           setLoading(false)
         }
       )

       return () => subscription.unsubscribe()
     }, [])

     const value = {
       user,
       loading,
       signUp: authService.signUp,
       signIn: authService.signIn,
       signOut: authService.signOut,
     }

     return (
       <AuthContext.Provider value={value}>
         {children}
       </AuthContext.Provider>
     )
   }
   ```

2. **Login/Signup Screens**
   - Create LoginScreen.tsx
   - Create SignupScreen.tsx
   - Add form validation
   - Handle auth errors

3. **Navigation Updates**
   ```javascript
   // Update App.tsx navigation based on auth state
   const { user, loading } = useAuth()

   if (loading) return <LoadingScreen />
   
   return user ? <AuthenticatedStack /> : <AuthStack />
   ```

## Migration Strategy

### Moving from Local to Backend Storage
1. **Keep existing local storage initially**
2. **Sync local data to backend** when user signs up
3. **Gradually migrate features** to use backend
4. **Add offline support** with local caching

### Data Migration Code
```javascript
// src/services/migrationService.js
export const migrationService = {
  async migrateLocalData(userId) {
    // Get existing local data
    const localFoods = await AsyncStorage.getItem('loggedFoods')
    if (!localFoods) return

    const foods = JSON.parse(localFoods)
    
    // Upload to backend
    for (const food of foods) {
      await foodLogService.logFood(
        {
          user_id: userId,
          food_name: food.name,
          serving_size: food.serving_size,
          quantity: food.quantity,
          meal_type: food.meal_type,
          logged_at: food.logged_at
        },
        {
          calories: food.calories,
          protein: food.protein,
          carbs: food.carbs,
          fat: food.fat,
          // ... other nutrition fields
        }
      )
    }

    // Clear local storage after successful migration
    await AsyncStorage.removeItem('loggedFoods')
  }
}
```

## Advanced Features to Add Later

### Real-time Updates
```javascript
// Subscribe to real-time changes
const subscription = supabase
  .channel('food_logs_changes')
  .on('postgres_changes', 
    { event: '*', schema: 'public', table: 'food_logs' },
    (payload) => {
      // Update UI in real-time
      console.log('Change received!', payload)
    }
  )
  .subscribe()
```

### Analytics Queries
```sql
-- Weekly nutrition trends
SELECT 
  DATE_TRUNC('week', date) as week,
  AVG(total_calories) as avg_calories,
  AVG(total_protein) as avg_protein
FROM daily_summaries 
WHERE user_id = $1 
GROUP BY week 
ORDER BY week DESC;

-- Most logged foods
SELECT 
  food_name,
  COUNT(*) as times_logged,
  AVG(calories) as avg_calories
FROM food_logs fl
JOIN nutrition_data nd ON fl.id = nd.food_log_id
WHERE user_id = $1
GROUP BY food_name
ORDER BY times_logged DESC
LIMIT 10;
```

## Security Considerations

1. **Row Level Security** - Enabled on all tables
2. **JWT Tokens** - Automatic handling by Supabase
3. **Environment Variables** - Never commit secrets
4. **API Rate Limiting** - Built into Supabase
5. **Data Validation** - Validate on both client and server

## Deployment Notes

### Environment Setup
```bash
# Development
SUPABASE_URL=your_dev_project_url
SUPABASE_ANON_KEY=your_dev_anon_key

# Production  
SUPABASE_URL=your_prod_project_url
SUPABASE_ANON_KEY=your_prod_anon_key
```

### Backup Strategy
- Supabase handles automated backups
- Export data periodically for additional safety
- Consider point-in-time recovery for production

## Cost Estimates (2025)

### Supabase Pricing
- **Free Tier**: 50,000 monthly active users, 500MB storage
- **Pro**: $25/month for 100,000 MAU, 8GB storage
- **Team**: $599/month for 500,000 MAU, 100GB storage

### Typical Food Tracking App Usage
- **Storage**: ~1MB per user per year (food logs + nutrition data)
- **API Requests**: ~1000 requests per user per month
- **Real-time**: Minimal usage for food tracking

**Expected costs for 1000 active users**: ~$25/month

## Implementation Timeline

- **Day 1**: Supabase setup + schema creation (4 hours)
- **Day 2**: Authentication integration (6 hours)  
- **Day 3**: Food logging service + UI updates (8 hours)
- **Day 4**: Testing + migration strategy (4 hours)
- **Day 5**: Polish + deployment (4 hours)

**Total**: ~26 hours of development time

## Next Steps When Ready

1. Create Supabase account and project
2. Set up database schema using the SQL above
3. Install Supabase dependencies in React Native
4. Implement authentication first
5. Gradually migrate features from local to backend storage
6. Add real-time features and analytics

---

*This guide provides a complete roadmap for adding backend functionality to the Food Scanner app. Implement when ready to scale beyond local storage.*