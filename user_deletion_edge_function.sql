-- PROPER USER DELETION IMPLEMENTATION
-- This replaces the previous user_deletion_function.sql

-- First, create an Edge Function (recommended by Supabase)
-- This should be created in your Supabase Dashboard > Edge Functions

/*
Edge Function: delete-user
File: supabase/functions/delete-user/index.ts

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the Auth context of the logged in user.
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    )

    // Get the current user
    const { data: { user } } = await supabaseClient.auth.getUser()
    if (!user) {
      return new Response(JSON.stringify({ error: 'Not authenticated' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 401
      })
    }

    // Create admin client for user deletion
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Delete user data first (RLS will ensure user can only delete their own data)
    const { error: dataError } = await supabaseClient.rpc('delete_user_data')
    if (dataError) {
      console.error('Error deleting user data:', dataError)
      return new Response(JSON.stringify({ error: 'Failed to delete user data' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      })
    }

    // Delete the user from auth (this properly invalidates JWT tokens)
    const { data, error } = await supabaseAdmin.auth.admin.deleteUser(user.id)
    if (error) {
      console.error('Error deleting user:', error)
      return new Response(JSON.stringify({ error: error.message }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      })
    }

    return new Response(JSON.stringify({ success: true, user_id: user.id }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('Error in delete-user function:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500
    })
  }
})
*/

-- Database function to delete user data (called by Edge Function)
-- This function runs with user privileges and RLS protection
CREATE OR REPLACE FUNCTION delete_user_data()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    user_id uuid;
    result json;
BEGIN
    -- Get the current user's ID
    user_id := auth.uid();
    
    -- Check if user is authenticated
    if user_id IS NULL then
        RETURN json_build_object('error', 'Not authenticated');
    END IF;
    
    -- Log the deletion attempt
    RAISE NOTICE 'Deleting data for user %', user_id;
    
    -- Delete user's data in the correct order (RLS policies ensure user can only delete own data)
    
    -- 1. Delete nutrition_data (references food_logs)
    DELETE FROM public.nutrition_data 
    WHERE food_log_id IN (
        SELECT id FROM public.food_logs WHERE user_id = user_id
    );
    
    -- 2. Delete food_logs (references profiles/users)  
    DELETE FROM public.food_logs WHERE user_id = user_id;
    
    -- 3. Delete daily_summaries (references profiles/users)
    DELETE FROM public.daily_summaries WHERE user_id = user_id;
    
    -- 4. Delete profile (references auth.users)
    DELETE FROM public.profiles WHERE id = user_id;
    
    -- Note: We do NOT delete from auth.users here - that's handled by the Edge Function
    -- using admin.deleteUser() which properly invalidates JWT tokens
    
    -- Return success
    RETURN json_build_object('success', true, 'user_id', user_id);
    
EXCEPTION WHEN others THEN
    -- Log the error and return it
    RAISE NOTICE 'Error deleting user data: %', SQLERRM;
    RETURN json_build_object('error', SQLERRM);
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION delete_user_data() TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION delete_user_data() IS 'Deletes all user data except auth.users record. Called by Edge Function before admin.deleteUser().';