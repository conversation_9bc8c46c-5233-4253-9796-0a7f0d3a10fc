import { TurboModule, TurboModuleRegistry } from 'react-native';

export interface Spec extends TurboModule {
  isLidarSupported(): Promise<boolean>;
  measureDistance(): Promise<{
    distance: number;
    confidence: number;
    quality: string;
    measurements: Record<string, number>;
    processingTime: number;
  }>;
  capturePhotoWithDepth(): Promise<{
    imagePath: string;
    distance: number;
    confidence: number;
    quality: string;
    measurements: Record<string, number>;
    processingTime: number;
  }>;
}

export default TurboModuleRegistry.getEnforcing<Spec>('LidarMeasurementModule');