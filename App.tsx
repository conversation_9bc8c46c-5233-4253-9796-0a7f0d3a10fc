import 'react-native-url-polyfill/auto';
import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  StatusBar,
  Alert,
  View,
  TouchableOpacity,
  ScrollView,
  Linking,
  Animated,
} from 'react-native';
import { CameraScreen } from './src/components/CameraScreen';
import { FoodResultsScreen } from './src/components/FoodResultsScreen';
import { HistoryScreen } from './src/components/HistoryScreen';
import {
  BrutalistText,
  BrutalistTabBar,
  TabKey,
  AuthNavigator,
  LoadingScreen,
  SetNewPasswordScreen,
  SwipeableItem,
  SwipeableItemRef,
  DeleteConfirmationModal,
  MealEditModal,
  AppHeader,
  ProfileModal,
  EmailVerificationErrorModal,
  InsightsBar,
  BrutalistView,
} from './src/components';
import { BrutalistTheme } from './src/theme/colors';
import { LoggedFood, MealSession, FoodItem } from './src/types/food';
import { RecognitionResult, aiService } from './src/services/aiService';
import { OPENAI_API_KEY } from '@env';
import { AuthProvider, useAuth } from './src/contexts/AuthContext';
import { foodLogService } from './src/services/foodLogService';
import { authService } from './src/services/authService';
import { supabase } from './src/services/supabase';
import { useInsights } from './src/hooks/useInsights';

const AppContent: React.FC = () => {
  const {
    user,
    profile,
    loading,
    isInitialized,
    isPasswordRecovery,
    dataVersion,
    completePasswordRecovery,
    setPasswordRecoveryMode,
    setRecoveryTokens,
    setCurrentAuthProvider,
    registerDataRefreshCallback,
  } = useAuth();
  const [activeTab, setActiveTab] = useState<TabKey>('today');
  const [recognitionResult, setRecognitionResult] =
    useState<RecognitionResult | null>(null);
  const [batchFoods, setBatchFoods] = useState<FoodItem[]>([]);
  const [mealSessions, setMealSessions] = useState<MealSession[]>([]);
  const [showFoodResults, setShowFoodResults] = useState(false);
  const [expandedSessions, setExpandedSessions] = useState<Set<string>>(
    new Set(),
  );
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    visible: boolean;
    type: 'session' | 'food';
    sessionId?: string;
    foodId?: string;
    title: string;
    message: string;
  } | null>(null);
  const [editFood, setEditFood] = useState<{
    visible: boolean;
    food: LoggedFood | null;
    session: MealSession | null;
  }>({ visible: false, food: null, session: null });
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [openSwipeItemId, setOpenSwipeItemId] = useState<string | null>(null);
  const swipeableRefs = useRef<Map<string, SwipeableItemRef>>(new Map());
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [emailVerificationError, setEmailVerificationError] = useState<{
    visible: boolean;
    errorCode?: string;
    errorMessage?: string;
  }>({ visible: false });
  const [showNutrientGoals, setShowNutrientGoals] = useState(false);
  const [searchRequested, setSearchRequested] = useState(false);
  const [minimumLoadingComplete, setMinimumLoadingComplete] = useState(false);

  // App entrance animation - blur reveal effect
  const appOpacity = useRef(new Animated.Value(0)).current;
  const appScale = useRef(new Animated.Value(1.05)).current;
  const blurOverlayOpacity = useRef(new Animated.Value(1)).current;

  // Insights hook
  const { currentInsight, loading: insightsLoading } = useInsights(user?.id);

  // Animate app entrance when loading is complete - blur reveal effect
  useEffect(() => {
    if (!loading && isInitialized && minimumLoadingComplete) {
      Animated.parallel([
        Animated.timing(appOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(appScale, {
          toValue: 1,
          duration: 700,
          useNativeDriver: true,
        }),
        Animated.timing(blurOverlayOpacity, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [
    loading,
    isInitialized,
    minimumLoadingComplete,
    appOpacity,
    appScale,
    blurOverlayOpacity,
  ]);

  // Helper function to reset food results state
  const resetFoodResultsState = useCallback(
    (
      options: {
        preserveCurrentSession?: boolean;
        navigateToToday?: boolean;
      } = {},
    ) => {
      setShowFoodResults(false);
      setRecognitionResult(null);
      setBatchFoods([]);
      setSearchRequested(false);

      if (!options.preserveCurrentSession) {
        setCurrentSessionId(null);
      }

      if (options.navigateToToday) {
        setActiveTab('today');
      }
    },
    [],
  );

  const loadAllFoods = useCallback(async () => {
    if (!user) return;

    console.log('📊 Starting to load meal sessions...');
    const startTime = performance.now();

    // Load ALL meal sessions (grouped by meal_session_id) for history screen
    const { data: sessions, error } = await foodLogService.getMealSessions(
      user.id,
      null,
    );

    const endTime = performance.now();
    console.log(
      `📊 Loaded ${sessions?.length || 0} sessions in ${Math.round(
        endTime - startTime,
      )}ms`,
    );

    if (error) {
      console.error('Failed to load meal sessions:', error);
      return;
    }

    if (sessions) {
      setMealSessions(sessions);
      console.log('✅ UI updated with meal sessions');
    }
  }, [user]);
  // Initialize AI service API key from environment variables
  React.useEffect(() => {
    if (OPENAI_API_KEY) {
      aiService.setApiKey(OPENAI_API_KEY);
      console.log('OpenAI API key configured');
    } else {
      console.warn('OpenAI API key not found. Please check your .env file.');
    }
  }, []);

  // Load all foods from Supabase when user is available or data version changes
  React.useEffect(() => {
    if (user) {
      loadAllFoods();
    }
  }, [user, dataVersion, loadAllFoods]);

  // Handle deep links for email verification
  useEffect(() => {
    const parseSupabaseUrl = (url: string) => {
      // Supabase uses # fragments but we need ? for URL params
      if (url.includes('#')) {
        return url.replace('#', '?');
      }
      return url;
    };

    const handleDeepLink = async (url: string) => {
      console.log('Deep link received:', url);

      if (url && (url.includes('://auth') || url.includes('koa://auth'))) {
        console.log('Auth deep link detected');

        // Parse Supabase URL format (replace # with ?)
        const parsedUrl = parseSupabaseUrl(url);
        const urlObj = new URL(parsedUrl);
        const params = urlObj.searchParams;

        console.log('Parsed URL params:', Object.fromEntries(params.entries()));

        // Check for error parameters first
        const error = params.get('error');
        const error_code = params.get('error_code');
        const error_description = params.get('error_description');

        if (error || error_code) {
          console.log('Auth error detected:', {
            error,
            error_code,
            error_description,
          });

          // Show error modal for both OAuth and email verification errors
          setTimeout(() => {
            setEmailVerificationError({
              visible: true,
              errorCode: error_code || error,
              errorMessage: error_description
                ? decodeURIComponent(error_description.replace(/\+/g, ' '))
                : undefined,
            });
          }, 500);
          return;
        }

        // If no errors, check for success tokens
        const access_token = params.get('access_token');
        const refresh_token = params.get('refresh_token');
        const type = params.get('type'); // Check if this is a password recovery
        const provider = params.get('provider'); // Check if this is OAuth

        if (access_token && refresh_token) {
          console.log(
            'Setting session with tokens, type:',
            type,
            'provider:',
            provider,
          );

          // Check if this is a password recovery link
          if (type === 'recovery') {
            console.log(
              'Password recovery link detected - storing tokens and entering recovery mode',
            );
            try {
              // Store recovery tokens for later use
              setRecoveryTokens({ access_token, refresh_token });
              // Set recovery mode to show password reset screen
              setPasswordRecoveryMode(true);
              console.log('Recovery tokens stored and recovery mode enabled');
            } catch (err) {
              console.error('Recovery setup failed:', err);
            }
            return;
          }

          // Handle OAuth or email verification
          try {
            // If this is OAuth, set the provider flag to prevent password recovery mode
            if (provider) {
              console.log(
                'OAuth authentication detected for provider:',
                provider,
                '- setting provider flag',
              );
              setCurrentAuthProvider(provider);
            }

            const { data, error: sessionError } =
              await supabase.auth.setSession({
                access_token,
                refresh_token,
              });

            if (!sessionError && data.session?.user) {
              console.log(
                'Authentication successful!',
                provider ? `via ${provider}` : 'via email verification',
              );

              // Create profile if it doesn't exist (for both OAuth and email verification)
              try {
                const { data: existingProfile } = await authService.getProfile(
                  data.session.user.id,
                );
                if (!existingProfile) {
                  const displayName =
                    data.session.user.user_metadata?.full_name ||
                    data.session.user.user_metadata?.name ||
                    data.session.user.email?.split('@')[0] ||
                    'User';

                  await authService.createProfile(data.session.user.id, {
                    full_name: displayName,
                  });
                }
              } catch (profileErr) {
                console.warn('Profile creation/check failed:', profileErr);
              }
            } else {
              console.error('Session setup failed:', sessionError);
            }
          } catch (err) {
            console.error('Session setup failed:', err);
          }
        }
      }
    };

    // Set up listeners
    Linking.getInitialURL().then(url => {
      if (url) handleDeepLink(url);
    });

    const subscription = Linking.addEventListener('url', ({ url }) => {
      handleDeepLink(url);
    });

    return () => subscription?.remove();
  }, [setCurrentAuthProvider, setPasswordRecoveryMode, setRecoveryTokens]);

  // Register data refresh callback with AuthContext for coordinated app resume
  useEffect(() => {
    if (user) {
      registerDataRefreshCallback(loadAllFoods);
    }
  }, [user, registerDataRefreshCallback, loadAllFoods]);

  // Deletion handlers for Today screen
  const handleDeleteFood = (
    foodId: string,
    sessionId: string,
    foodName: string,
  ) => {
    setDeleteConfirmation({
      visible: true,
      type: 'food',
      sessionId,
      foodId,
      title: 'DELETE FOOD ITEM',
      message: `Remove "${foodName}" from today's ${
        sessionId ? 'meal' : 'log'
      }? This will update your daily totals.`,
    });
  };

  const handleDeleteSession = (
    sessionId: string,
    mealType: string,
    itemCount: number,
  ) => {
    setDeleteConfirmation({
      visible: true,
      type: 'session',
      sessionId,
      title: 'DELETE MEAL SESSION',
      message: `Remove this entire ${mealType} session (${itemCount} items) from today? This will update your daily totals.`,
    });
  };

  const confirmDeletion = async () => {
    if (!deleteConfirmation || !user) return;

    try {
      if (deleteConfirmation.type === 'food' && deleteConfirmation.foodId) {
        const { error } = await foodLogService.deleteFoodItem(
          user.id,
          deleteConfirmation.foodId,
        );
        if (error) throw error;
      } else if (
        deleteConfirmation.type === 'session' &&
        deleteConfirmation.sessionId
      ) {
        const { error } = await foodLogService.deleteMealSession(
          user.id,
          deleteConfirmation.sessionId,
        );
        if (error) throw error;
      }

      // Refresh data
      await loadAllFoods();
    } catch (error) {
      Alert.alert('Error', 'Failed to delete item. Please try again.');
      console.error('Delete error:', error);
    }

    setDeleteConfirmation(null);
  };

  const cancelDeletion = () => {
    setDeleteConfirmation(null);
  };

  // Edit handlers for Today screen
  const handleEditMealSession = (session: MealSession) => {
    setEditFood({ visible: true, food: null, session });
  };

  const handleSaveEditedFood = async (editedFood: LoggedFood) => {
    if (!user) return;

    console.log(
      'handleSaveEditedFood called with:',
      editedFood.name,
      'quantity:',
      editedFood.quantity,
    );

    try {
      const { error } = await foodLogService.updateFoodItem(
        user.id,
        editedFood.id,
        editedFood,
      );

      if (error) {
        console.error('Database update error:', error);
        throw error;
      }

      console.log('Successfully updated food in database');

      // Refresh data
      await loadAllFoods();

      // Update daily summary
      const today = new Date().toISOString().split('T')[0];
      await foodLogService.updateDailySummary(user.id, today);
    } catch (error) {
      Alert.alert('Error', 'Failed to update food item. Please try again.');
      console.error('Update error:', error);
    }
  };

  const handleCancelEdit = () => {
    setEditFood({ visible: false, food: null, session: null });
  };

  const handleRescan = () => {
    // Store the current session ID to continue adding to it
    if (editFood.session) {
      setCurrentSessionId(editFood.session.id);
    }
    // Close the edit modal
    setEditFood({ visible: false, food: null, session: null });
    // Switch to camera tab
    setActiveTab('scan');
  };

  const handleAddFoodToSession = async (
    sessionId: string,
    food: Omit<LoggedFood, 'id' | 'meal_session_id'>,
  ) => {
    if (!user) return;
    try {
      // We only need to pass the raw food data, the service handles the rest
      await foodLogService.addFoodToSession(user.id, sessionId, food);
      await loadAllFoods(); // Refresh data
    } catch (error) {
      Alert.alert(
        'Error',
        'Failed to add new food to the meal. Please try again.',
      );
      console.error('Add food to session error:', error);
    }
  };

  const handleUpdateDishName = async (sessionId: string, dishName: string) => {
    if (!user) return;
    try {
      await foodLogService.updateSessionDishName(user.id, sessionId, dishName);
      await loadAllFoods(); // Refresh data
    } catch (error) {
      Alert.alert('Error', 'Failed to update dish name. Please try again.');
      console.error('Update dish name error:', error);
    }
  };

  // Swipe management handlers for Today screen
  const handleSwipeStart = (itemId: string) => {
    // Close any currently open item
    if (openSwipeItemId && openSwipeItemId !== itemId) {
      const currentOpenRef = swipeableRefs.current.get(openSwipeItemId);
      if (currentOpenRef) {
        currentOpenRef.reset();
      }
    }
    setOpenSwipeItemId(itemId);
  };

  const handleSwipeReset = () => {
    setOpenSwipeItemId(null);
  };

  const setSwipeableRef = (itemId: string, ref: SwipeableItemRef | null) => {
    if (ref) {
      swipeableRefs.current.set(itemId, ref);
    } else {
      swipeableRefs.current.delete(itemId);
    }
  };

  const handleFoodRecognized = (result: RecognitionResult) => {
    if (batchFoods.length > 0) {
      // Batch mode: add new foods to existing batch
      const combinedFoods = [...batchFoods, ...result.foods];
      setRecognitionResult({
        ...result,
        foods: combinedFoods,
      });
    } else {
      // Normal mode: use result as-is (empty or not)
      setRecognitionResult(result);
    }
    setShowFoodResults(true);
  };

  const handleLogFood = async (food: LoggedFood) => {
    if (!user) {
      Alert.alert('Error', 'You must be logged in to save food logs');
      return;
    }

    try {
      // Save to Supabase as a single-item meal session for consistency
      const { data: savedFoods, error } = await foodLogService.logMealSession(
        user.id,
        [food],
      );

      if (error) {
        console.error('Failed to save food:', error);
        Alert.alert('Error', 'Failed to save food log. Please try again.');
        return;
      }

      if (savedFoods) {
        // Reload all foods to ensure consistency
        await loadAllFoods();

        // Update daily summary
        const today = new Date().toISOString().split('T')[0];
        await foodLogService.updateDailySummary(user.id, today);
      }

      resetFoodResultsState({ navigateToToday: true });
    } catch (err) {
      console.error('Error saving food:', err);
      Alert.alert('Error', 'Failed to save food log. Please try again.');
    }
  };

  const handleLogMealSession = async (foods: LoggedFood[]) => {
    if (!user) {
      Alert.alert('Error', 'You must be logged in to save food logs');
      return;
    }

    try {
      // If we're in rescan mode, only save the newly scanned foods to the existing session
      let foodsToSave = foods;
      if (currentSessionId && recognitionResult) {
        // Filter out existing foods - only save the new ones from the current scan
        const existingFoodCount =
          mealSessions.find(s => s.id === currentSessionId)?.foods.length || 0;
        foodsToSave = foods.slice(existingFoodCount); // Take only the new foods
      }

      // Save foods as a meal session to Supabase
      // Use currentSessionId if available (from rescan), otherwise create new session
      const { data: savedFoods, error } = await foodLogService.logMealSession(
        user.id,
        foodsToSave,
        currentSessionId ?? undefined, // mealSessionId
        recognitionResult?.dish_name, // dishName
      );

      if (error) {
        console.error('Failed to save meal session:', error);
        Alert.alert('Error', 'Failed to save meal. Please try again.');
        return;
      }

      if (savedFoods) {
        // Reload all foods to ensure consistency
        await loadAllFoods();

        // Update daily summary
        const today = new Date().toISOString().split('T')[0];
        await foodLogService.updateDailySummary(user.id, today);
      }

      resetFoodResultsState({ navigateToToday: true });
    } catch (err) {
      console.error('Error saving meal session:', err);
      Alert.alert('Error', 'Failed to save meal. Please try again.');
    }
  };

  const handleRetakePicture = () => {
    // Check if we have batch foods or additional foods that would be lost
    const hasBatchItems = batchFoods.length > 0;
    const hasRecognizedFoods =
      recognitionResult && recognitionResult.foods.length > 0;

    if (hasBatchItems || hasRecognizedFoods) {
      const itemCount =
        batchFoods.length + (recognitionResult?.foods.length || 0);
      Alert.alert(
        'Clear All Items?',
        `This will remove ${itemCount} item${
          itemCount > 1 ? 's' : ''
        } from your batch and start over.`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Clear & Retake',
            style: 'destructive',
            onPress: () => {
              resetFoodResultsState();
            },
          },
        ],
      );
    } else {
      // No items to lose, just go back
      resetFoodResultsState();
    }
  };

  const handleBatchScan = () => {
    // Store current foods for batch mode
    if (recognitionResult) {
      setBatchFoods(recognitionResult.foods);
    }
    // Partial reset - preserve batch foods and current session
    setRecognitionResult(null);
    setShowFoodResults(false);
    setSearchRequested(false);
    setActiveTab('scan');
  };

  const handleManualAdd = () => {
    // Just go to food results screen with current batch (same as SCAN MORE)
    setRecognitionResult({
      foods: batchFoods.length > 0 ? [...batchFoods] : [],
      overall_confidence: 100,
    });
    setShowFoodResults(true);
  };

  const handleSearchRequested = () => {
    // Go to food results screen with no foods but request search to open
    setRecognitionResult({
      foods: batchFoods.length > 0 ? [...batchFoods] : [],
      overall_confidence: 100,
    });
    setSearchRequested(true);
    setShowFoodResults(true);
  };

  const handleTabPress = (tab: TabKey) => {
    setActiveTab(tab);
    // Clear food results when switching tabs (but preserve batch if going to scan)
    if (tab !== 'scan' && showFoodResults) {
      resetFoodResultsState();
    }
  };

  const handleProfilePress = () => {
    setShowProfileModal(true);
  };

  const handleCloseProfile = () => {
    setShowProfileModal(false);
  };

  const handleCloseEmailVerificationError = () => {
    setEmailVerificationError({ visible: false });
  };

  // Dynamic StatusBar style based on active tab
  const getStatusBarStyle = () => {
    return activeTab === 'scan' ? 'dark-content' : 'dark-content';
  };

  const getStatusBarBackground = () => {
    return activeTab === 'scan'
      ? 'rgba(248, 248, 252, 1)' // Match app background
      : 'rgba(248, 248, 252, 1)'; // Match our new app background
  };

  const toggleSessionExpanded = (sessionId: string) => {
    setExpandedSessions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sessionId)) {
        newSet.delete(sessionId);
      } else {
        newSet.add(sessionId);
      }
      return newSet;
    });
  };

  const getTodaysNutrition = () => {
    const today = new Date().toDateString();
    const todaysSessions = mealSessions.filter(
      session => new Date(session.logged_at).toDateString() === today,
    );

    // Flatten all foods from today's sessions
    const todaysFoods = todaysSessions.flatMap(session => session.foods);

    return {
      calories: todaysFoods.reduce(
        (sum, food) => sum + food.calories * food.quantity,
        0,
      ),
      protein: todaysFoods.reduce(
        (sum, food) => sum + food.protein * food.quantity,
        0,
      ),
      carbs: todaysFoods.reduce(
        (sum, food) => sum + food.carbs * food.quantity,
        0,
      ),
      fat: todaysFoods.reduce((sum, food) => sum + food.fat * food.quantity, 0),
      sessions: todaysSessions,
      sessionCount: todaysSessions.length,
    };
  };

  const getNutrientGoalPercentages = () => {
    const nutrition = getTodaysNutrition();

    return {
      protein: Math.round(
        (nutrition.protein / (profile?.daily_protein_goal || 150)) * 100,
      ),
      carbs: Math.round(
        (nutrition.carbs / (profile?.daily_carb_goal || 250)) * 100,
      ),
      fat: Math.round((nutrition.fat / (profile?.daily_fat_goal || 65)) * 100),
    };
  };

  const renderTodayScreen = () => {
    const nutrition = getTodaysNutrition();
    const nutrientGoals = getNutrientGoalPercentages();

    return (
      <View style={styles.screenContainer}>
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.content}>
            {/* Summary Section - Warm Purple Tint */}
            <BrutalistView
              variant="floating-attached"
              padding="md"
              style={{ marginBottom: BrutalistTheme.spacing.sm }}
            >
              <BrutalistText
                variant="tabLabelMedium"
                color="grey700"
                style={styles.summaryHeader}
              >
                today's nutrition
              </BrutalistText>

              {/* Clean metrics row */}
              <View style={styles.todayMetrics}>
                <View style={styles.metricItem}>
                  <BrutalistText
                    variant="headline"
                    weight="bold"
                    fontFamily="mono"
                    color="grey800"
                  >
                    {nutrition.sessionCount}
                  </BrutalistText>
                  <BrutalistText
                    variant="caption2"
                    weight="medium"
                    color="grey600"
                  >
                    MEALS
                  </BrutalistText>
                </View>

                <TouchableOpacity
                  style={styles.metricItem}
                  onPress={() => setShowNutrientGoals(!showNutrientGoals)}
                  activeOpacity={0.7}
                >
                  <BrutalistText
                    variant="headline"
                    weight="bold"
                    fontFamily="mono"
                    color="grey900"
                  >
                    {Math.round(
                      (nutrition.calories /
                        (profile?.daily_calorie_goal || 2000)) *
                        100,
                    )}
                    %
                  </BrutalistText>
                  <BrutalistText
                    variant="caption2"
                    weight="medium"
                    color={showNutrientGoals ? 'accent2' : 'grey600'}
                  >
                    GOAL {showNutrientGoals ? '−' : '+'}
                  </BrutalistText>
                </TouchableOpacity>

                <View style={styles.metricItem}>
                  <BrutalistText
                    variant="headline"
                    weight="bold"
                    fontFamily="mono"
                    color="accent1"
                  >
                    {Math.round(nutrition.calories)}
                  </BrutalistText>
                  <BrutalistText
                    variant="caption2"
                    weight="medium"
                    color="grey600"
                  >
                    CALORIES
                  </BrutalistText>
                </View>
              </View>

              {/* Expandable Nutrient Goals - attached inside same container */}
              {showNutrientGoals && (
                <View style={styles.nutrientGoalsExpanded}>
                  <View style={styles.nutrientGoalsGrid}>
                    <View style={styles.nutrientGoalItem}>
                      <BrutalistText
                        variant="headline"
                        weight="bold"
                        fontFamily="mono"
                        color="accent1"
                      >
                        {nutrientGoals.protein}%
                      </BrutalistText>
                      <BrutalistText
                        variant="caption1"
                        weight="medium"
                        color="grey600"
                      >
                        PROTEIN
                      </BrutalistText>
                    </View>

                    <View style={styles.nutrientGoalItem}>
                      <BrutalistText
                        variant="headline"
                        weight="bold"
                        fontFamily="mono"
                        color="accent1"
                      >
                        {nutrientGoals.carbs}%
                      </BrutalistText>
                      <BrutalistText
                        variant="caption1"
                        weight="medium"
                        color="grey600"
                      >
                        CARBS
                      </BrutalistText>
                    </View>

                    <View style={styles.nutrientGoalItem}>
                      <BrutalistText
                        variant="headline"
                        weight="bold"
                        fontFamily="mono"
                        color="accent1"
                      >
                        {nutrientGoals.fat}%
                      </BrutalistText>
                      <BrutalistText
                        variant="caption1"
                        weight="medium"
                        color="grey600"
                      >
                        FAT
                      </BrutalistText>
                    </View>
                  </View>
                </View>
              )}
            </BrutalistView>

            {/* Recent meals - soft card system */}
            {nutrition.sessions.length > 0 && (
              <View style={styles.recentMealsSection}>
                <BrutalistView
                  variant="glass"
                  padding="md"
                  style={{ marginBottom: BrutalistTheme.spacing.sm }}
                >
                  <BrutalistText
                    variant="tabLabelMedium"
                    color="grey700"
                    style={styles.recentMealsHeader}
                  >
                    recent meals
                  </BrutalistText>
                  <View style={styles.mealsContainer}>
                    {nutrition.sessions.slice(0, 5).map((session, _index) => {
                      const isExpanded = expandedSessions.has(session.id);
                      return (
                        <SwipeableItem
                          key={session.id}
                          ref={ref =>
                            setSwipeableRef(`today-session-${session.id}`, ref)
                          }
                          itemId={`today-session-${session.id}`}
                          onDelete={() =>
                            handleDeleteSession(
                              session.id,
                              session.meal_type,
                              session.total_items,
                            )
                          }
                          onEdit={() => handleEditMealSession(session)}
                          onSwipeStart={handleSwipeStart}
                          onSwipeReset={handleSwipeReset}
                          deleteText="DELETE"
                          editText="EDIT"
                          deleteThreshold={40}
                        >
                          <TouchableOpacity
                            style={styles.mealSessionContainer}
                            onPress={
                              session.total_items > 1
                                ? () => toggleSessionExpanded(session.id)
                                : undefined
                            }
                            activeOpacity={session.total_items > 1 ? 0.7 : 1}
                          >
                            <View style={styles.mealRow}>
                              <View style={styles.mealInfo}>
                                <View style={styles.timeAndMealGroup}>
                                  <BrutalistText
                                    variant="caption1"
                                    weight="medium"
                                    color="grey600"
                                    fontFamily="mono"
                                    style={styles.timeLabel}
                                  >
                                    {new Date(
                                      session.logged_at,
                                    ).toLocaleTimeString([], {
                                      hour: '2-digit',
                                      minute: '2-digit',
                                    })}
                                  </BrutalistText>
                                  <BrutalistText
                                    variant="body"
                                    weight="regular"
                                    color="grey800"
                                  >
                                    {session.meal_type}
                                  </BrutalistText>
                                  {session.dish_name && (
                                    <BrutalistText
                                      variant="footnote"
                                      color="accent1"
                                      fontFamily="serif"
                                      style={styles.dishName}
                                    >
                                      {session.dish_name}
                                    </BrutalistText>
                                  )}
                                </View>
                                <View>
                                  <BrutalistText
                                    variant="caption1"
                                    weight="medium"
                                    color={isExpanded ? 'accent2' : 'grey600'}
                                  >
                                    {session.total_items === 1
                                      ? `${
                                          session.foods[0]?.name || 'Item'
                                        } • ${session.foods[0]?.quantity || 1}×`
                                      : `${session.total_items} items${
                                          isExpanded ? ' −' : ' +'
                                        }`}
                                  </BrutalistText>
                                </View>
                              </View>

                              <BrutalistText
                                variant="callout"
                                weight="semibold"
                                color="accent1"
                                fontFamily="mono"
                              >
                                {Math.round(session.total_calories)}
                              </BrutalistText>
                            </View>

                            {/* Expanded Individual Foods */}
                            {isExpanded && (
                              <View style={styles.expandedFoods}>
                                {session.foods.map((food, foodIndex) => (
                                  <SwipeableItem
                                    key={`${food.id}-${foodIndex}`}
                                    ref={ref =>
                                      setSwipeableRef(
                                        `today-food-${food.id}-${foodIndex}`,
                                        ref,
                                      )
                                    }
                                    itemId={`today-food-${food.id}-${foodIndex}`}
                                    onDelete={() =>
                                      handleDeleteFood(
                                        food.id,
                                        session.id,
                                        food.name,
                                      )
                                    }
                                    onSwipeStart={handleSwipeStart}
                                    onSwipeReset={handleSwipeReset}
                                    deleteText="DEL"
                                    editText="EDIT"
                                    deleteThreshold={40}
                                  >
                                    <View style={styles.foodItemRow}>
                                      <BrutalistText
                                        variant="caption1"
                                        weight="medium"
                                        color="grey600"
                                      >
                                        {food.name} • {food.quantity}×
                                      </BrutalistText>
                                    </View>
                                  </SwipeableItem>
                                ))}
                              </View>
                            )}
                          </TouchableOpacity>
                        </SwipeableItem>
                      );
                    })}

                    {nutrition.sessions.length > 5 && (
                      <BrutalistText
                        variant="bodySmall"
                        color="grey600"
                        style={styles.moreItemsText}
                      >
                        +{nutrition.sessions.length - 5} more in history
                      </BrutalistText>
                    )}
                  </View>
                </BrutalistView>
              </View>
            )}

            {/* Daily Insights Bar - moved to bottom */}
            <InsightsBar insight={currentInsight} loading={insightsLoading} />

            {/* Empty state */}
            {nutrition.sessions.length === 0 && (
              <View style={styles.emptyState}>
                <BrutalistText
                  variant="title3"
                  weight="medium"
                  color="grey600"
                  style={styles.emptyMessage}
                >
                  No meals logged today
                </BrutalistText>
                <BrutalistText
                  variant="body"
                  color="grey600"
                  fontFamily="serif"
                >
                  Start by scanning your first meal
                </BrutalistText>
              </View>
            )}
          </View>
        </ScrollView>

        {/* Delete Confirmation Modal */}
        {deleteConfirmation && (
          <DeleteConfirmationModal
            visible={deleteConfirmation.visible}
            title={deleteConfirmation.title}
            message={deleteConfirmation.message}
            onConfirm={confirmDeletion}
            onCancel={cancelDeletion}
            confirmText="DELETE"
            cancelText="CANCEL"
          />
        )}
      </View>
    );
  };

  // Show FoodResults screen as a modal overlay when food is recognized
  if (showFoodResults && recognitionResult) {
    // If we're in rescan mode (currentSessionId exists), combine with existing session foods
    const existingSession = currentSessionId
      ? mealSessions.find(session => session.id === currentSessionId)
      : null;

    const allFoods = existingSession
      ? [
          ...existingSession.foods.map((food, index) => ({
            id: `existing-${food.id}-${index}`, // Ensure unique keys for existing foods
            name: food.name,
            calories: food.calories,
            protein: food.protein,
            carbs: food.carbs,
            fat: food.fat,
            serving_size: food.serving_size,
            confidence: food.confidence,
            fiber: food.fiber,
            sugar: food.sugar,
            sodium: food.sodium,
            cholesterol: food.cholesterol,
            portion_count: food.portion_count,
            portion_description: food.portion_description,
            portion_weight: food.portion_weight,
            portion_weight_unit: food.portion_weight_unit,
          })),
          ...recognitionResult.foods.map((food, index) => ({
            ...food,
            id: `new-${food.id}-${index}`, // Ensure unique keys for new foods
          })),
        ]
      : recognitionResult.foods;

    return (
      <FoodResultsScreen
        foods={allFoods}
        confidence={recognitionResult.overall_confidence}
        dishName={recognitionResult.dish_name}
        onLogFood={handleLogFood}
        onLogMealSession={handleLogMealSession}
        onRetakePicture={handleRetakePicture}
        onBatchScan={handleBatchScan}
        autoOpenSearch={searchRequested}
      />
    );
  }

  // Main tab-based navigation
  const renderCurrentTab = () => {
    switch (activeTab) {
      case 'scan':
        return (
          <CameraScreen
            onFoodRecognized={handleFoodRecognized}
            onManualAdd={handleManualAdd}
            onSearchRequested={handleSearchRequested}
          />
        );

      case 'today':
        return renderTodayScreen();

      case 'history':
        return (
          <HistoryScreen
            mealSessions={mealSessions}
            userId={user?.id || ''}
            onDataChanged={loadAllFoods}
            onEditMealSession={handleEditMealSession}
          />
        );

      default:
        return renderTodayScreen();
    }
  };

  // Show loading screen while checking auth state OR before initialization completes OR minimum loading time not met
  if (loading || !isInitialized || !minimumLoadingComplete) {
    return (
      <>
        <LoadingScreen
          minimumDuration={2000}
          onMinimumDurationComplete={() => setMinimumLoadingComplete(true)}
        />
        {/* Email Verification Error Modal only - success shown in main app */}
        <EmailVerificationErrorModal
          visible={emailVerificationError.visible}
          onClose={handleCloseEmailVerificationError}
          errorCode={emailVerificationError.errorCode}
          errorMessage={emailVerificationError.errorMessage}
        />
      </>
    );
  }

  // Show password recovery screen if in password recovery mode
  if (isPasswordRecovery) {
    return <SetNewPasswordScreen onComplete={completePasswordRecovery} />;
  }

  // Show auth screens if not logged in
  if (!user) {
    return (
      <>
        <AuthNavigator />
        {/* Email Verification Error Modal only - success shown in main app */}
        <EmailVerificationErrorModal
          visible={emailVerificationError.visible}
          onClose={handleCloseEmailVerificationError}
          errorCode={emailVerificationError.errorCode}
          errorMessage={emailVerificationError.errorMessage}
        />
      </>
    );
  }

  // Show main app if logged in
  return (
    <View style={styles.appContainer}>
      <Animated.View
        style={[
          styles.appContainer,
          {
            opacity: appOpacity,
            transform: [{ scale: appScale }],
          },
        ]}
      >
        <SafeAreaView style={styles.appContainer}>
          <StatusBar
            barStyle={getStatusBarStyle()}
            backgroundColor={getStatusBarBackground()}
          />
          <AppHeader
            title={activeTab === 'today' ? 'today' : activeTab}
            variant={activeTab === 'scan' ? 'camera' : 'minimal'}
            onProfilePress={handleProfilePress}
          />
          <View style={styles.mainContent}>{renderCurrentTab()}</View>
          <BrutalistTabBar activeTab={activeTab} onTabPress={handleTabPress} />

          {/* Profile Modal - Available on all screens */}
          <ProfileModal
            visible={showProfileModal}
            onClose={handleCloseProfile}
          />

          {/* Edit Meal Modal - Available on all screens */}
          <MealEditModal
            visible={editFood.visible}
            session={editFood.session}
            onSave={handleSaveEditedFood}
            onAddFoodToSession={handleAddFoodToSession}
            onUpdateDishName={handleUpdateDishName}
            onCancel={handleCancelEdit}
            onRescan={handleRescan}
          />
        </SafeAreaView>
      </Animated.View>

      {/* Blur reveal overlay */}
      <Animated.View
        style={[styles.blurOverlay, { opacity: blurOverlayOpacity }]}
        pointerEvents="none"
      />
    </View>
  );
};

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

const styles = StyleSheet.create({
  appContainer: {
    flex: 1,
    backgroundColor: 'rgba(248, 248, 252, 1)', // Very subtle purple-tinted white
  },
  mainContent: {
    flex: 1,
  },
  screenContainer: {
    flex: 1,
    backgroundColor: 'rgba(248, 248, 252, 1)', // Subtle tinted background
  },
  container: {
    flex: 1,
    backgroundColor: 'rgba(248, 248, 252, 1)', // Subtle tinted background
  },
  scrollView: {
    flex: 1,
  },

  // Content Layout
  content: {
    flex: 1,

    paddingBottom: BrutalistTheme.spacing.lg,
    gap: 0, // Reduced from sm (8px) to xs (4px)
  },

  // Summary header styling (now inside BrutalistView)
  summaryHeader: {
    letterSpacing: 1,
    textAlign: 'center',
    marginBottom: 5,
  },

  // Recent meals header - left aligned with underline
  recentMealsHeader: {
    letterSpacing: 1,
    textAlign: 'left',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(88, 86, 214, 0.08)', // Subtle purple tinted underline
    paddingBottom: BrutalistTheme.spacing.xs, // Small padding before underline
  },

  // Today's Metrics - clean grid
  todayMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  metricItem: {
    alignItems: 'center',
    flex: 1,
    gap: 1,
  },

  // Recent Meals Section
  recentMealsSection: {
    gap: 0,
  },
  mealsContainer: {
    gap: 0,
  },
  mealSessionContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(88, 86, 214, 0.06)', // Subtle purple tinted divider
    marginBottom: 0,
    paddingVertical: BrutalistTheme.spacing.sm,
  },
  mealRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 0, // Remove padding to align with title
    backgroundColor: 'transparent',
  },
  expandedFoods: {
    paddingBottom: 0,

    gap: 0,
  },
  foodItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: BrutalistTheme.spacing.xs,
  },
  foodItemContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: BrutalistTheme.spacing.sm,
  },
  mealInfo: {
    flex: 1,
    gap: 1,
  },
  timeAndMealGroup: {
    gap: 1,
  },
  timeLabel: {
    letterSpacing: 0.5,
    marginBottom: -4,
  },
  moreItemsText: {
    textAlign: 'center',
    marginTop: BrutalistTheme.spacing.sm,
    fontStyle: 'italic',
  },

  // Empty State
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: BrutalistTheme.spacing.xxxl,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    gap: BrutalistTheme.spacing.md,
  },
  emptyMessage: {
    textAlign: 'center',
  },
  foodItemRow: {
    paddingVertical: 0,
    backgroundColor: BrutalistTheme.colors.white,
  },

  // Nutrient Goals Expanded Section
  nutrientGoalsExpanded: {
    marginTop: BrutalistTheme.spacing.sm,
    paddingTop: BrutalistTheme.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: 'rgba(88, 86, 214, 0.08)', // Subtle purple divider
  },

  // Nutrient Goals Grid (now inside BrutalistView)
  nutrientGoalsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  nutrientGoalItem: {
    alignItems: 'center',
    flex: 1,
    gap: 1,
  },
  dishName: {
    marginTop: 0,
    fontStyle: 'italic',
    lineHeight: 14,
    marginBottom: 1,
  },
  blurOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    zIndex: 1000,
  },
});

export default App;
