-- Add dish_name support to food_logs table
-- This allows storing the overall dish name (e.g., "grilled salmon with vegetables") 
-- alongside individual food items for better meal context

-- Add dish_name column to food_logs table
ALTER TABLE food_logs 
ADD COLUMN dish_name TEXT;

-- Add comment to document the field
COMMENT ON COLUMN food_logs.dish_name IS 'Overall dish/meal name recognized by AI (e.g., "grilled salmon with vegetables")';

-- Create index for faster dish name searches
CREATE INDEX idx_food_logs_dish_name ON food_logs(dish_name) WHERE dish_name IS NOT NULL;