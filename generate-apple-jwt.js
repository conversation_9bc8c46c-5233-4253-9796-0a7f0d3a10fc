const crypto = require('crypto');
const fs = require('fs');

// Your Apple credentials
const TEAM_ID = '2U897A827R';
const KEY_ID = '4V95B4PA77';
const CLIENT_ID = 'com.slippa.koa';
const PRIVATE_KEY_PATH = './AuthKey_4V95B4PA77.p8'; // Update this path to your .p8 file

function base64URLEncode(str) {
  return Buffer.from(str)
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

function generateAppleJWT() {
  try {
    // Check if .p8 file exists
    if (!fs.existsSync(PRIVATE_KEY_PATH)) {
      console.error(`❌ .p8 file not found at: ${PRIVATE_KEY_PATH}`);
      console.log('Please place your AuthKey_4V95B4PA77.p8 file in the same directory as this script');
      return;
    }

    // Read the private key
    const privateKey = fs.readFileSync(PRIVATE_KEY_PATH, 'utf8');

    // Create header
    const header = {
      alg: 'ES256',
      kid: KEY_ID
    };

    // Create payload
    const now = Math.floor(Date.now() / 1000);
    const payload = {
      iss: TEAM_ID,
      iat: now,
      exp: now + (180 * 24 * 60 * 60), // 6 months from now
      aud: 'https://appleid.apple.com',
      sub: CLIENT_ID
    };

    // Encode header and payload
    const encodedHeader = base64URLEncode(JSON.stringify(header));
    const encodedPayload = base64URLEncode(JSON.stringify(payload));

    // Create signature
    const signingInput = `${encodedHeader}.${encodedPayload}`;
    const signature = crypto.sign('sha256', Buffer.from(signingInput), {
      key: privateKey,
      format: 'pem'
    });
    const encodedSignature = base64URLEncode(signature);

    // Create JWT
    const jwt = `${encodedHeader}.${encodedPayload}.${encodedSignature}`;

    console.log('✅ Apple JWT generated successfully!');
    console.log('\n📋 Copy this JWT and paste it into Supabase Secret Key field:');
    console.log('----------------------------------------');
    console.log(jwt);
    console.log('----------------------------------------');
    console.log('\n🔍 JWT Components for debugging:');
    console.log('Header:', JSON.stringify(header));
    console.log('Payload:', JSON.stringify(payload));
    console.log('Header (base64):', encodedHeader);
    console.log('Payload (base64):', encodedPayload);
    console.log('Signature (base64):', encodedSignature);
    console.log('\n📝 Instructions:');
    console.log('1. Copy the JWT above');
    console.log('2. Go to Supabase → Authentication → Providers → Apple');
    console.log('3. Paste it in the "Secret Key (for OAuth)" field');
    console.log('4. Click Save');

  } catch (error) {
    console.error('❌ Error generating JWT:', error.message);
    
    if (error.message.includes('no start line')) {
      console.log('\n🔧 Fix: Make sure your .p8 file contains the full private key including:');
      console.log('-----BEGIN PRIVATE KEY-----');
      console.log('(key content)');
      console.log('-----END PRIVATE KEY-----');
    }
  }
}

console.log('🍎 Apple JWT Generator for Supabase');
console.log('===================================');
generateAppleJWT();