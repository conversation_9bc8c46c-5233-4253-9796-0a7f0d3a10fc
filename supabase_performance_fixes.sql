-- Fix for duplicate index
DROP INDEX IF EXISTS public.idx_food_logs_user_logged_at;

-- Drop existing policies before creating new ones
-- food_logs policies
DROP POLICY IF EXISTS "Users can view own food logs" ON public.food_logs;
DROP POLICY IF EXISTS "Users can insert own food logs" ON public.food_logs;
DROP POLICY IF EXISTS "Users can update own food logs" ON public.food_logs;
DROP POLICY IF EXISTS "Users can delete own food logs" ON public.food_logs;
DROP POLICY IF EXISTS food_logs_user_access ON public.food_logs;

-- nutrition_data policies
DROP POLICY IF EXISTS "Users can view nutrition data for own food logs" ON public.nutrition_data;
DROP POLICY IF EXISTS "Users can insert nutrition data for own food logs" ON public.nutrition_data;
DROP POLICY IF EXISTS "Users can update nutrition data for own food logs" ON public.nutrition_data;
DROP POLICY IF EXISTS "Users can delete nutrition data for own food logs" ON public.nutrition_data;
DROP POLICY IF EXISTS nutrition_data_user_access ON public.nutrition_data;

-- daily_summaries policies
DROP POLICY IF EXISTS "Users can view own daily summaries" ON public.daily_summaries;
DROP POLICY IF EXISTS "Users can insert own daily summaries" ON public.daily_summaries;
DROP POLICY IF EXISTS "Users can update own daily summaries" ON public.daily_summaries;
DROP POLICY IF EXISTS daily_summaries_user_access ON public.daily_summaries;


-- Recreate policies with performance optimizations

-- food_logs policies
CREATE POLICY "Users can view own food logs" ON public.food_logs FOR SELECT USING ((select auth.uid()) = user_id);
CREATE POLICY "Users can insert own food logs" ON public.food_logs FOR INSERT WITH CHECK ((select auth.uid()) = user_id);
CREATE POLICY "Users can update own food logs" ON public.food_logs FOR UPDATE USING ((select auth.uid()) = user_id);
CREATE POLICY "Users can delete own food logs" ON public.food_logs FOR DELETE USING ((select auth.uid()) = user_id);

-- nutrition_data policies
CREATE POLICY "Users can view nutrition data for own food logs" ON public.nutrition_data FOR SELECT USING (EXISTS (SELECT 1 FROM food_logs WHERE food_logs.id = nutrition_data.food_log_id AND food_logs.user_id = (select auth.uid())));
CREATE POLICY "Users can insert nutrition data for own food logs" ON public.nutrition_data FOR INSERT WITH CHECK (EXISTS (SELECT 1 FROM food_logs WHERE food_logs.id = nutrition_data.food_log_id AND food_logs.user_id = (select auth.uid())));
CREATE POLICY "Users can update nutrition data for own food logs" ON public.nutrition_data FOR UPDATE USING (EXISTS (SELECT 1 FROM food_logs WHERE food_logs.id = nutrition_data.food_log_id AND food_logs.user_id = (select auth.uid())));
CREATE POLICY "Users can delete nutrition data for own food logs" ON public.nutrition_data FOR DELETE USING (EXISTS (SELECT 1 FROM food_logs WHERE food_logs.id = nutrition_data.food_log_id AND food_logs.user_id = (select auth.uid())));

-- daily_summaries policies
CREATE POLICY "Users can view own daily summaries" ON public.daily_summaries FOR SELECT USING ((select auth.uid()) = user_id);
CREATE POLICY "Users can insert own daily summaries" ON public.daily_summaries FOR INSERT WITH CHECK ((select auth.uid()) = user_id);
CREATE POLICY "Users can update own daily summaries" ON public.daily_summaries FOR UPDATE USING ((select auth.uid()) = user_id);
