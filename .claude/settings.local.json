{"permissions": {"allow": ["Bash(open ios/FoodScannerMobile.xcworkspace)", "Bash(xcodebuild:*)", "Bash(rm:*)", "Bash(pod install:*)", "Bash(npx react-native run-ios:*)", "Bash(npx react-native start:*)", "Bash(kill:*)", "Bash(npm install:*)", "Bash(npm uninstall:*)", "Bash(VCEnableFrameProcessors=false pod install)", "WebFetch(domain:developer.apple.com)", "<PERSON><PERSON>(mv:*)", "WebFetch(domain:github.com)", "WebFetch(domain:supabase.com)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(uuidgen)", "Bash(npx pod-install:*)", "WebFetch(domain:medium.com)", "WebFetch(domain:arxiv.org)", "WebSearch", "WebFetch(domain:openaccess.thecvf.com)", "WebFetch(domain:www.foodvisor.io)", "WebFetch(domain:prior.allenai.org)", "WebFetch(domain:www.sciencedirect.com)", "WebFetch(domain:platform.openai.com)", "<PERSON><PERSON>(curl:*)", "Bash(npx react-native-asset:*)", "Bash(grep:*)", "Bash(cp:*)", "WebFetch(domain:www.frontiersin.org)"], "deny": [], "defaultMode": "acceptEdits"}}