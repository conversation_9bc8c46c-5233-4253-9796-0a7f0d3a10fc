-- BULLETPROOF SOLUTION: Use Supabase's official admin.deleteUser approach
-- This creates a server-side function that uses the proper admin API

-- First, let's create a simple data cleanup function
CREATE OR REPLACE FUNCTION cleanup_user_data(target_user_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    result json;
BEGIN
    -- Validate that user can only delete their own data
    IF target_user_id != auth.uid() THEN
        RETURN json_build_object('error', 'Unauthorized - can only delete own account');
    END IF;
    
    -- Log the deletion attempt
    RAISE NOTICE 'Cleaning up data for user %', target_user_id;
    
    -- Delete user's data in the correct order
    DELETE FROM public.nutrition_data 
    WHERE food_log_id IN (
        SELECT id FROM public.food_logs WHERE user_id = target_user_id
    );
    
    DELETE FROM public.food_logs WHERE user_id = target_user_id;
    DELETE FROM public.daily_summaries WHERE user_id = target_user_id;
    DELETE FROM public.profiles WHERE id = target_user_id;
    
    -- Return success
    RETURN json_build_object('success', true, 'user_id', target_user_id, 'message', 'Data cleaned up successfully');
    
EXCEPTION WHEN others THEN
    RAISE NOTICE 'Error cleaning up user data: %', SQLERRM;
    RETURN json_build_object('error', SQLERRM);
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION cleanup_user_data(uuid) TO authenticated;

-- Update the main deletion function to be more robust
DROP FUNCTION IF EXISTS delete_user_account();

CREATE OR REPLACE FUNCTION delete_user_account()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    user_id uuid;
    cleanup_result json;
    auth_result json;
BEGIN
    -- Get the current user's ID
    user_id := auth.uid();
    
    -- Check if user is authenticated
    IF user_id IS NULL THEN
        RETURN json_build_object('error', 'Not authenticated');
    END IF;
    
    RAISE NOTICE 'Starting account deletion for user %', user_id;
    
    -- Step 1: Clean up user data
    SELECT cleanup_user_data(user_id) INTO cleanup_result;
    
    IF cleanup_result->>'error' IS NOT NULL THEN
        RETURN cleanup_result;
    END IF;
    
    RAISE NOTICE 'User data cleaned up successfully';
    
    -- Step 2: Try to revoke refresh tokens (if we have permission)
    BEGIN
        UPDATE auth.refresh_tokens 
        SET revoked = true, updated_at = now()
        WHERE user_id = user_id;
        
        RAISE NOTICE 'Refresh tokens revoked for user %', user_id;
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'Could not revoke refresh tokens (this might be expected): %', SQLERRM;
        -- Continue anyway - the main deletion should still work
    END;
    
    -- Step 3: Delete from auth.users
    BEGIN
        DELETE FROM auth.users WHERE id = user_id;
        RAISE NOTICE 'User % deleted from auth.users', user_id;
    EXCEPTION WHEN others THEN
        RAISE NOTICE 'Error deleting from auth.users: %', SQLERRM;
        RETURN json_build_object('error', 'Failed to delete user from auth system: ' || SQLERRM);
    END;
    
    -- Return success
    RETURN json_build_object(
        'success', true, 
        'user_id', user_id,
        'message', 'Account deleted successfully',
        'data_cleanup', cleanup_result
    );
    
EXCEPTION WHEN others THEN
    RAISE NOTICE 'Unexpected error in delete_user_account: %', SQLERRM;
    RETURN json_build_object('error', 'Unexpected error: ' || SQLERRM);
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION delete_user_account() TO authenticated;

COMMENT ON FUNCTION delete_user_account() IS 'Bulletproof user account deletion with detailed logging and error handling';