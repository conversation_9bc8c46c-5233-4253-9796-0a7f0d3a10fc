-- IMMEDIATE FIX: Replace the existing delete_user_account function
-- This version properly handles JWT invalidation

DROP FUNCTION IF EXISTS delete_user_account();

-- Updated function that uses admin.deleteUser approach
CREATE OR REPLACE FUNCTION delete_user_account()
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER -- This allows the function to run with elevated privileges
SET search_path = public
AS $$
DECLARE
    user_id uuid;
    result json;
BEGIN
    -- Get the current user's ID
    user_id := auth.uid();
    
    -- Check if user is authenticated
    if user_id IS NULL then
        RETURN json_build_object('error', 'Not authenticated');
    END IF;
    
    -- Log the deletion attempt
    RAISE NOTICE 'User % is deleting their account', user_id;
    
    -- Delete user's data in the correct order (children first, then parents)
    
    -- 1. Delete nutrition_data (references food_logs)
    DELETE FROM public.nutrition_data 
    WHERE food_log_id IN (
        SELECT id FROM public.food_logs WHERE user_id = user_id
    );
    
    -- 2. Delete food_logs (references profiles/users)  
    DELETE FROM public.food_logs WHERE user_id = user_id;
    
    -- 3. Delete daily_summaries (references profiles/users)
    DELETE FROM public.daily_summaries WHERE user_id = user_id;
    
    -- 4. Delete profile (references auth.users)
    DELETE FROM public.profiles WHERE id = user_id;
    
    -- 5. Delete from auth.users AND invalidate all sessions
    -- This is the critical fix - we need to revoke all refresh tokens first
    UPDATE auth.refresh_tokens 
    SET revoked = true 
    WHERE user_id = user_id;
    
    -- Then delete the user
    DELETE FROM auth.users WHERE id = user_id;
    
    -- Return success
    RETURN json_build_object('success', true, 'user_id', user_id);
    
EXCEPTION WHEN others THEN
    -- Log the error and return it
    RAISE NOTICE 'Error deleting user account: %', SQLERRM;
    RETURN json_build_object('error', SQLERRM);
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION delete_user_account() TO authenticated;

-- Add comment for documentation  
COMMENT ON FUNCTION delete_user_account() IS 'Properly deletes user account including revoking refresh tokens to invalidate sessions.';