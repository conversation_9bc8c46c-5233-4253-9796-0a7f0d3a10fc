#!/usr/bin/env ruby

require 'xcodeproj'

# Open the Xcode project
project_path = 'FoodScannerMobile.xcodeproj'
project = Xcodeproj::Project.open(project_path)

# Find the main target
target = project.targets.find { |t| t.name == 'FoodScannerMobile' }

# Find the main group
main_group = project.main_group.find_subpath('FoodScannerMobile')

# Add ARSessionCoordinator.swift to the project
arsession_file = main_group.new_file('ARSessionCoordinator.swift')

# Add the file to the target
target.add_file_references([arsession_file])

# Save the project
project.save

puts "✅ ARSessionCoordinator.swift added to Xcode project"
puts "🔧 Next steps:"
puts "1. Run 'cd ios && pod install'"
puts "2. Open FoodScannerMobile.xcworkspace in Xcode"
puts "3. Clean and rebuild the project"
