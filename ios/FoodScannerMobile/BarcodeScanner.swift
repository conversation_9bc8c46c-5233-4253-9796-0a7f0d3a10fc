import Foundation
import React
import Vision
import ARKit

@objc(BarcodeScanner)
class BarcodeScanner: NSObject, RCTBridgeModule {

    static func moduleName() -> String! {
        return "BarcodeScanner"
    }

    @objc
    static func requiresMainQueueSetup() -> <PERSON><PERSON> {
        return true
    }

    @objc func scanBarcode(
        _ resolve: @escaping RCTPromiseResolveBlock,
        rejecter reject: @escaping RCTPromiseRejectBlock
    ) {
        NSLog("🔍 [BarcodeScanner] Starting barcode scan from ARKit frame...")
        
        DispatchQueue.main.async {
            // Get ARSession from coordinator if available
            guard let arSession = ARSessionCoordinator.shared.session else {
                NSLog("❌ [BarcodeScanner] No AR session available")
                reject("NO_AR_SESSION", "No AR session available for barcode scanning", nil)
                return
            }
            
            guard let frame = arSession.currentFrame else {
                NSLog("❌ [BarcodeScanner] No current frame available")
                reject("NO_FRAME", "No AR frame available for barcode scanning", nil)
                return
            }
            
            NSLog("✅ [BarcodeScanner] Got current frame, processing for barcodes...")
            self.detectBarcodesInFrame(frame) { barcodes in
                if let barcode = barcodes.first {
                    NSLog("🎯 [BarcodeScanner] Found barcode: \(barcode.payloadStringValue ?? "unknown")")
                    
                    let result: [String: Any] = [
                        "found": true,
                        "type": barcode.symbology.rawValue,
                        "value": barcode.payloadStringValue ?? "",
                        "confidence": barcode.confidence,
                        "bounds": [
                            "x": barcode.boundingBox.origin.x,
                            "y": barcode.boundingBox.origin.y,
                            "width": barcode.boundingBox.size.width,
                            "height": barcode.boundingBox.size.height
                        ]
                    ]
                    
                    resolve(result)
                } else {
                    NSLog("📷 [BarcodeScanner] No barcodes detected in current frame")
                    resolve([
                        "found": false,
                        "message": "No barcode detected in current frame"
                    ])
                }
            }
        }
    }
    
    @objc func checkForBarcode(
        _ resolve: @escaping RCTPromiseResolveBlock,
        rejecter reject: @escaping RCTPromiseRejectBlock
    ) {
        // Quick check for barcode presence without full processing
        DispatchQueue.main.async {
            guard let arSession = ARSessionCoordinator.shared.session else {
                resolve(["found": false, "message": "No AR session"])
                return
            }
            
            guard let frame = arSession.currentFrame else {
                resolve(["found": false, "message": "No frame"])
                return
            }
            
            self.detectBarcodesInFrame(frame) { barcodes in
                resolve(["found": !barcodes.isEmpty])
            }
        }
    }
    
    private func detectBarcodesInFrame(_ frame: ARFrame, completion: @escaping ([VNBarcodeObservation]) -> Void) {
        let pixelBuffer = frame.capturedImage
        
        NSLog("🔍 [BarcodeScanner] Processing CVPixelBuffer for barcode detection...")
        
        let barcodeRequest = VNDetectBarcodesRequest { request, error in
            DispatchQueue.main.async {
                if let error = error {
                    NSLog("❌ [BarcodeScanner] Vision framework error: \(error.localizedDescription)")
                    completion([])
                    return
                }
                
                let barcodes = request.results as? [VNBarcodeObservation] ?? []
                NSLog("🔍 [BarcodeScanner] Vision framework found \(barcodes.count) barcodes")
                
                for (index, barcode) in barcodes.enumerated() {
                    NSLog("📱 [BarcodeScanner] Barcode \(index + 1): type=\(barcode.symbology.rawValue), value=\(barcode.payloadStringValue ?? "nil"), confidence=\(barcode.confidence)")
                }
                
                completion(barcodes)
            }
        }
        
        // Support common food product barcode types
        barcodeRequest.symbologies = [
            .ean8, .ean13, .upce, .code128, .qr, .code39, .code93, .itf14, .dataMatrix, .pdf417
        ]
        
        DispatchQueue.global(qos: .userInitiated).async {
            let handler = VNImageRequestHandler(cvPixelBuffer: pixelBuffer, orientation: .up, options: [:])
            
            do {
                try handler.perform([barcodeRequest])
                NSLog("✅ [BarcodeScanner] Vision framework processing completed")
            } catch {
                NSLog("❌ [BarcodeScanner] Failed to perform barcode detection: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    completion([])
                }
            }
        }
    }
}