import ARKit
import Foundation
import React
import UIKit

@objc(LidarMeasurementModule)
class LidarMeasurementModule: NSObject, RCTBridgeModule {

    static func moduleName() -> String! {
        return "LidarMeasurementModule"
    }

    static func requiresMainQueueSetup() -> Bool {
        return true
    }

    // MARK: - Private Properties
    private var arSession: ARSession?
    private var arView: ARSCNView?
    private var measurementPoints: [simd_float3] = []

    // MARK: - Public Methods

    @objc func isLidarSupported(
        _ resolve: @escaping RCTPromiseResolveBlock,
        rejecter reject: @escaping RCTPromiseRejectBlock
    ) {
        NSLog("🔍 [LidarMeasurement] Checking LiDAR support...")

        DispatchQueue.main.async {
            // Check if device supports LiDAR (scene depth)
            let supportsLidar = ARWorldTrackingConfiguration.supportsFrameSemantics(.sceneDepth)
            NSLog("📱 [LidarMeasurement] LiDAR support: \(supportsLidar ? "YES" : "NO")")
            resolve(supportsLidar)
        }
    }

    @objc func measureDistance(
        _ resolve: @escaping RCTPromiseResolveBlock,
        rejecter reject: @escaping RCTPromiseRejectBlock
    ) {
        NSLog("📏 [LidarMeasurement] Starting distance measurement session...")

        DispatchQueue.main.async {
            self.performMeasurement(resolve: resolve, reject: reject)
        }
    }
    @objc func capturePhotoWithDepth(
        _ resolve: @escaping RCTPromiseResolveBlock,
        rejecter reject: @escaping RCTPromiseRejectBlock
    ) {
        NSLog("📸 [LidarMeasurement] Capturing photo with depth via ARKit...")

        DispatchQueue.main.async {
            guard ARWorldTrackingConfiguration.supportsFrameSemantics(.sceneDepth) else {
                NSLog("❌ [LidarMeasurement] LiDAR not supported on this device")
                reject("LIDAR_NOT_SUPPORTED", "Device does not support LiDAR measurements", nil)
                return
            }

            // Reuse existing preview ARSession if available, otherwise create a short-lived one
            let arSession: ARSession
            if let existing = ARSessionCoordinator.shared.session {
                NSLog("🔄 [LidarMeasurement] Using shared AR session")
                arSession = existing
            } else {
                NSLog("🚀 [LidarMeasurement] Creating new AR session")
                arSession = ARSession()
                let configuration = ARWorldTrackingConfiguration()
                // Only enable depth data - no plane detection needed for food scanning
                configuration.frameSemantics = [.sceneDepth, .smoothedSceneDepth]

                // Run session with minimal options for faster startup
                arSession.run(configuration)
                self.arSession = arSession

                NSLog("🚀 [LidarMeasurement] Started new AR session optimized for food scanning")
            }

            // Capture immediately - no delay needed with shared session
            self.captureFromCurrentFrame(session: arSession, resolve: resolve, reject: reject)
        }
    }

    private func captureFromCurrentFrame(
        session: ARSession,
        resolve: @escaping RCTPromiseResolveBlock,
        reject: @escaping RCTPromiseRejectBlock
    ) {
        // Capture immediately - no retry needed with shared session
        let startTime = CFAbsoluteTimeGetCurrent()

        // Get current frame immediately
        guard let frame = session.currentFrame else {
            NSLog("❌ [LidarMeasurement] No current frame available for immediate capture")
            reject("NO_FRAME", "No AR frame available for capture", nil)
            return
        }

        // Process the captured frame immediately
        NSLog(
            "🔍 [LidarMeasurement] Frame info - tracking state: \(frame.camera.trackingState), timestamp: \(frame.timestamp)"
        )
        NSLog("🔍 [LidarMeasurement] Frame has depth data: \(frame.sceneDepth != nil)")
        NSLog("🔍 [LidarMeasurement] Frame has smoothed depth: \(frame.smoothedSceneDepth != nil)")

        // Convert frame.capturedImage (CVPixelBuffer) to JPEG and save to temp file
        let pixelBuffer = frame.capturedImage
        let ciImage = CIImage(cvPixelBuffer: pixelBuffer)
        let context = CIContext(options: nil)

        guard let cgImage = context.createCGImage(ciImage, from: ciImage.extent) else {
            NSLog("❌ [LidarMeasurement] Failed to create CGImage from CIImage")
            reject("IMAGE_CONVERSION_FAILED", "Could not convert image for saving", nil)
            return
        }

        // Orientation: ARKit's capturedImage is in camera sensor orientation; rotate to portrait
        let uiImage = UIImage(cgImage: cgImage, scale: 1.0, orientation: .right)
        guard let jpegData = uiImage.jpegData(compressionQuality: 0.9) else {
            NSLog("❌ [LidarMeasurement] Failed to create JPEG data")
            reject("JPEG_FAILED", "Could not create JPEG data", nil)
            return
        }

        let tmpDir = FileManager.default.temporaryDirectory
        let fileName = "koa_capture_\(Int(Date().timeIntervalSince1970 * 1000)).jpg"
        let fileURL = tmpDir.appendingPathComponent(fileName)

        do {
            try jpegData.write(to: fileURL, options: .atomic)
        } catch {
            NSLog("❌ [LidarMeasurement] Failed to write image: \(error.localizedDescription)")
            reject("FILE_WRITE_FAILED", "Could not save image to temporary file", error)
            return
        }

        // Compute center raycast distance and confidence/quality
        // Use actual camera resolution instead of hardcoded screen size
        let imageSize = CGSize(
            width: CVPixelBufferGetWidth(pixelBuffer), height: CVPixelBufferGetHeight(pixelBuffer))
        NSLog("🔍 [LidarMeasurement] Using image size: \(imageSize)")

        let centerPoint = CGPoint(x: imageSize.width * 0.5, y: imageSize.height * 0.5)

        var distances: [String: Float] = [:]
        var validMeasurements = 0
        let pointNames = ["center", "topLeft", "topRight", "bottomLeft", "bottomRight"]
        let points = [
            centerPoint,
            CGPoint(x: imageSize.width * 0.3, y: imageSize.height * 0.3),
            CGPoint(x: imageSize.width * 0.7, y: imageSize.height * 0.3),
            CGPoint(x: imageSize.width * 0.3, y: imageSize.height * 0.7),
            CGPoint(x: imageSize.width * 0.7, y: imageSize.height * 0.7),
        ]

        NSLog("🎯 [LidarMeasurement] Measurement points: \(points)")
        NSLog("🎯 [LidarMeasurement] Starting direct depth measurements...")

        for (index, point) in points.enumerated() {
            NSLog("🎯 [LidarMeasurement] Measuring depth for \(pointNames[index]) at \(point)")
            if let distance = self.getDepthAtPoint(point, frame: frame) {
                distances[pointNames[index]] = distance
                validMeasurements += 1
                NSLog(
                    "✅ [LidarMeasurement] \(pointNames[index]): \(String(format: "%.2f", distance))cm"
                )
            } else {
                NSLog("❌ [LidarMeasurement] \(pointNames[index]): No measurement")
            }
        }

        NSLog(
            "📊 [LidarMeasurement] Measurement summary: \(validMeasurements)/\(points.count) successful"
        )
        NSLog("📊 [LidarMeasurement] All distances: \(distances)")

        let centerDistance = distances["center"] ?? distances.values.first ?? 0
        let confidence = self.calculateConfidence(distances: Array(distances.values))
        let quality = self.assessQuality(
            confidence: confidence, measurementCount: validMeasurements)
        let processingTime = (CFAbsoluteTimeGetCurrent() - startTime) * 1000

        NSLog(
            "📊 [LidarMeasurement] Final results - Distance: \(String(format: "%.2f", centerDistance))cm, Confidence: \(String(format: "%.2f", confidence)), Quality: \(quality)"
        )

        let result: [String: Any] = [
            "imagePath": "file://\(fileURL.path)",
            "distance": centerDistance,
            "confidence": confidence,
            "quality": quality,
            "measurements": distances,
            "processingTime": processingTime,
            "debugInfo": "Enhanced debugging active - validMeasurements: \(validMeasurements)",
        ]

        NSLog(
            "✅ [LidarMeasurement] Photo+depth captured: path=\(fileName), dist=\(String(format: "%.1f", centerDistance))cm"
        )
        resolve(result)
    }

    // MARK: - Legacy retry method removed - using immediate capture instead

    // MARK: - Private Methods

    private func performMeasurement(
        resolve: @escaping RCTPromiseResolveBlock,
        reject: @escaping RCTPromiseRejectBlock
    ) {
        guard ARWorldTrackingConfiguration.supportsFrameSemantics(.sceneDepth) else {
            NSLog("❌ [LidarMeasurement] LiDAR not supported on this device")
            reject("LIDAR_NOT_SUPPORTED", "Device does not support LiDAR measurements", nil)
            return
        }

        NSLog("🚀 [LidarMeasurement] Creating temporary AR session...")

        // Create temporary AR session for measurement
        let arSession = ARSession()
        let configuration = ARWorldTrackingConfiguration()
        configuration.frameSemantics = [.sceneDepth, .smoothedSceneDepth]
        configuration.planeDetection = [.horizontal, .vertical]

        // Start session
        arSession.run(configuration)
        self.arSession = arSession

        // Wait a brief moment for session to initialize, then take measurement
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.takeMeasurement(session: arSession, resolve: resolve, reject: reject)
        }
    }

    private func takeMeasurement(
        session: ARSession,
        resolve: @escaping RCTPromiseResolveBlock,
        reject: @escaping RCTPromiseRejectBlock
    ) {
        let startTime = CFAbsoluteTimeGetCurrent()
        NSLog("📐 [LidarMeasurement] Taking raycast measurements...")

        guard let frame = session.currentFrame else {
            NSLog("❌ [LidarMeasurement] No current frame available")
            reject("NO_FRAME", "No AR frame available for measurement", nil)
            return
        }

        // Convert normalized points to screen coordinates
        let screenSize = CGSize(width: 390, height: 844)  // iPhone screen size approximation
        let measurementPoints = [
            CGPoint(x: screenSize.width * 0.5, y: screenSize.height * 0.5),  // Center
            CGPoint(x: screenSize.width * 0.3, y: screenSize.height * 0.3),  // Top-left
            CGPoint(x: screenSize.width * 0.7, y: screenSize.height * 0.3),  // Top-right
            CGPoint(x: screenSize.width * 0.3, y: screenSize.height * 0.7),  // Bottom-left
            CGPoint(x: screenSize.width * 0.7, y: screenSize.height * 0.7),  // Bottom-right
        ]

        var distances: [String: Float] = [:]
        var validMeasurements = 0

        // Take measurements at multiple points
        let pointNames = ["center", "topLeft", "topRight", "bottomLeft", "bottomRight"]

        for (index, point) in measurementPoints.enumerated() {
            if let distance = self.raycastDistance(from: point, frame: frame) {
                distances[pointNames[index]] = distance
                validMeasurements += 1
                NSLog(
                    "📍 [LidarMeasurement] \(pointNames[index]): \(String(format: "%.2f", distance))cm"
                )
            }
        }

        guard validMeasurements > 0 else {
            NSLog("❌ [LidarMeasurement] No valid measurements obtained")
            reject("NO_MEASUREMENTS", "Could not obtain any distance measurements", nil)
            return
        }

        // Calculate center distance (primary measurement)
        let centerDistance = distances["center"] ?? distances.values.first ?? 0

        // Calculate confidence based on measurement consistency
        let confidence = self.calculateConfidence(distances: Array(distances.values))
        let quality = self.assessQuality(
            confidence: confidence, measurementCount: validMeasurements)

        let processingTime = (CFAbsoluteTimeGetCurrent() - startTime) * 1000

        let result: [String: Any] = [
            "distance": centerDistance,
            "confidence": confidence,
            "quality": quality,
            "measurements": distances,
            "processingTime": processingTime,
        ]

        NSLog(
            "✅ [LidarMeasurement] Measurement complete: \(String(format: "%.1f", centerDistance))cm, confidence: \(String(format: "%.2f", confidence)), quality: \(quality)"
        )

        resolve(result)
    }

    private func raycastDistance(from point: CGPoint, frame: ARFrame) -> Float? {
        // Get the session - try shared session first, then stored session
        let session: ARSession
        if let sharedSession = ARSessionCoordinator.shared.session {
            session = sharedSession
            NSLog("✅ [LidarMeasurement] Using shared AR session for raycast")
        } else if let storedSession = self.arSession {
            session = storedSession
            NSLog("✅ [LidarMeasurement] Using stored AR session for raycast")
        } else {
            NSLog("❌ [LidarMeasurement] No AR session available for raycast")
            return nil
        }

        // Create raycast query from screen point using the frame
        let raycastQuery = frame.raycastQuery(
            from: point,
            allowing: .estimatedPlane,
            alignment: .any
        )

        // Perform raycast using the session
        let raycastResults = session.raycast(raycastQuery)

        NSLog(
            "🎯 [LidarMeasurement] Raycast at point \(point): found \(raycastResults.count) results")

        guard let firstResult = raycastResults.first else {
            NSLog(
                "⚠️ [LidarMeasurement] No raycast results for point \(point), trying direct depth lookup"
            )
            return self.getDepthAtPoint(point, frame: frame)
        }

        // Calculate distance from camera to hit point
        let cameraPosition = simd_float3(
            frame.camera.transform.columns.3.x,
            frame.camera.transform.columns.3.y,
            frame.camera.transform.columns.3.z
        )
        let hitPosition = simd_float3(
            firstResult.worldTransform.columns.3.x,
            firstResult.worldTransform.columns.3.y,
            firstResult.worldTransform.columns.3.z
        )
        let distance = simd_distance(cameraPosition, hitPosition)

        // Convert to centimeters and ensure reasonable range
        let distanceCM = distance * 100

        NSLog(
            "📏 [LidarMeasurement] Distance calculated: \(String(format: "%.2f", distanceCM))cm at point \(point)"
        )

        if distanceCM > 5 && distanceCM < 500 {
            return distanceCM
        } else {
            NSLog(
                "⚠️ [LidarMeasurement] Distance \(String(format: "%.2f", distanceCM))cm out of range (5-500cm)"
            )
            return nil
        }
    }

    // MARK: - Direct Depth Lookup
    private func getDepthAtPoint(_ point: CGPoint, frame: ARFrame) -> Float? {
        guard let depthData = frame.smoothedSceneDepth ?? frame.sceneDepth else {
            NSLog("❌ [LidarMeasurement] No depth data available for direct lookup")
            return nil
        }

        let depthMap = depthData.depthMap
        let width = CVPixelBufferGetWidth(depthMap)
        let height = CVPixelBufferGetHeight(depthMap)

        // Convert image coordinates to depth map coordinates
        let depthX = Int(point.x * CGFloat(width) / 1920.0)
        let depthY = Int(point.y * CGFloat(height) / 1440.0)

        // Ensure coordinates are within bounds
        guard depthX >= 0 && depthX < width && depthY >= 0 && depthY < height else {
            NSLog("❌ [LidarMeasurement] Depth coordinates out of bounds: (\(depthX), \(depthY))")
            return nil
        }

        CVPixelBufferLockBaseAddress(depthMap, .readOnly)
        defer { CVPixelBufferUnlockBaseAddress(depthMap, .readOnly) }

        guard let baseAddress = CVPixelBufferGetBaseAddress(depthMap) else {
            NSLog("❌ [LidarMeasurement] Could not get depth buffer base address")
            return nil
        }

        let bytesPerRow = CVPixelBufferGetBytesPerRow(depthMap)
        let depthPointer = baseAddress.assumingMemoryBound(to: Float32.self)
        let depthValue = depthPointer[depthY * (bytesPerRow / 4) + depthX]

        // Convert to centimeters and validate
        let distanceCM = depthValue * 100

        NSLog(
            "📏 [LidarMeasurement] Direct depth at \(point): \(String(format: "%.2f", distanceCM))cm"
        )

        if distanceCM > 5 && distanceCM < 500 && !depthValue.isNaN && !depthValue.isInfinite {
            return distanceCM
        } else {
            NSLog("⚠️ [LidarMeasurement] Invalid depth value: \(depthValue)")
            return nil
        }
    }

    private func calculateConfidence(distances: [Float]) -> Float {
        guard distances.count > 1 else { return 0.5 }

        let mean = distances.reduce(0, +) / Float(distances.count)
        let variance = distances.map { pow($0 - mean, 2) }.reduce(0, +) / Float(distances.count)
        let standardDeviation = sqrt(variance)

        // Lower standard deviation = higher confidence
        let normalizedStdDev = min(standardDeviation / mean, 1.0)
        return max(0.1, 1.0 - normalizedStdDev)
    }

    private func assessQuality(confidence: Float, measurementCount: Int) -> String {
        if confidence > 0.8 && measurementCount >= 4 {
            return "excellent"
        } else if confidence > 0.6 && measurementCount >= 3 {
            return "good"
        } else if confidence > 0.4 && measurementCount >= 2 {
            return "fair"
        } else {
            return "poor"
        }
    }

    private func cleanupSession() {
        NSLog("🧹 [LidarMeasurement] Cleaning up AR session...")
        DispatchQueue.main.async {
            self.arSession?.pause()
            self.arSession = nil
            self.arView = nil
        }
    }
}
