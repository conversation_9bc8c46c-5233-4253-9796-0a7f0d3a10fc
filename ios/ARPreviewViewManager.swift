import ARKit
import Foundation
import React
import UIKit

@objc(ARPreviewViewManager)
class ARPreviewViewManager: RCTViewManager {

    override func view() -> UIView! {
        return ARPreviewView()
    }

    override static func requiresMainQueueSetup() -> <PERSON><PERSON> {
        return true
    }
}

class ARPreviewView: UIView, ARSessionDelegate {
    private var arView: ARSCNView!
    private var session: ARSession!
    private var isSessionRunning = false

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupARView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupARView()
    }

    private func setupARView() {
        // Create ARSCNView
        arView = ARSCNView(frame: bounds)
        arView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        arView.delegate = self
        addSubview(arView)

        // Setup AR session
        session = ARSession()
        session.delegate = self
        arView.session = session

        // Use shared session if available, otherwise create new one
        if let sharedSession = ARSessionCoordinator.shared.session {
            print("🔄 [ARPreview] Using shared AR session")
            arView.session = sharedSession
            self.session = sharedSession
        } else {
            print("🚀 [ARPreview] Creating new AR session")
            startARSession()
            ARSessionCoordinator.shared.session = session
        }
    }

    private func startARSession() {
        let configuration = ARWorldTrackingConfiguration()
        
        // Check if LiDAR is supported and enable depth data if available
        if ARWorldTrackingConfiguration.supportsFrameSemantics(.sceneDepth) {
            configuration.frameSemantics = [.sceneDepth, .smoothedSceneDepth]
            print("✅ [ARPreview] LiDAR supported - enabling depth data")
        } else {
            print("⚠️ [ARPreview] LiDAR not supported - using basic AR tracking")
        }

        // No plane detection or scene reconstruction needed for food scanning
        session.run(configuration)
        isSessionRunning = true

        print("✅ [ARPreview] AR session started")
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        arView.frame = bounds
    }

    // MARK: - Lifecycle

    override func willMove(toSuperview newSuperview: UIView?) {
        super.willMove(toSuperview: newSuperview)

        if newSuperview != nil {
            // View is being added to hierarchy
            if !isSessionRunning && session != nil {
                startARSession()
            }
        } else {
            // View is being removed from hierarchy
            // Don't pause session here as it might be shared
            print("🔄 [ARPreview] View removed from hierarchy")
        }
    }

    // MARK: - ARSessionDelegate

    func session(_ session: ARSession, didFailWithError error: Error) {
        print("❌ [ARPreview] AR session failed: \(error)")
        isSessionRunning = false
    }

    func sessionWasInterrupted(_ session: ARSession) {
        print("⚠️ [ARPreview] AR session interrupted")
        isSessionRunning = false
    }

    func sessionInterruptionEnded(_ session: ARSession) {
        print("✅ [ARPreview] AR session interruption ended")
        if !isSessionRunning {
            startARSession()
        }
    }

    func session(_ session: ARSession, cameraDidChangeTrackingState camera: ARCamera) {
        DispatchQueue.main.async {
            switch camera.trackingState {
            case .normal:
                print("📱 [ARPreview] Camera tracking: Normal")
            case .notAvailable:
                print("❌ [ARPreview] Camera tracking: Not Available")
            case .limited(let reason):
                print("⚠️ [ARPreview] Camera tracking limited: \(reason)")
            }
        }
    }
}

// MARK: - ARSCNViewDelegate Extension
extension ARPreviewView: ARSCNViewDelegate {

    func renderer(_ renderer: SCNSceneRenderer, didAdd node: SCNNode, for anchor: ARAnchor) {
        // Handle plane detection if needed
        if let planeAnchor = anchor as? ARPlaneAnchor {
            print("🔍 [ARPreview] Detected plane: \(planeAnchor.classification)")
        }
    }

    func renderer(_ renderer: SCNSceneRenderer, didUpdate node: SCNNode, for anchor: ARAnchor) {
        // Handle plane updates if needed
    }

    func renderer(_ renderer: SCNSceneRenderer, didRemove node: SCNNode, for anchor: ARAnchor) {
        // Handle plane removal if needed
    }
}
