import { supabase } from './supabase'
import { LoggedFood, MealSession } from '../types/food'
import uuid from 'react-native-uuid'
import { localStorageService } from './localStorageService'
import { syncQueueService } from './syncQueueService'

// Timeout wrapper for background sync operations to prevent stuck UI states
const withSyncTimeout = async <T>(
  operation: () => Promise<T>,
  timeoutMs: number = 8000,
  operationName: string = 'sync operation'
): Promise<T> => {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error(`${operationName} timed out after ${timeoutMs}ms`)), timeoutMs);
  });
  
  return Promise.race([operation(), timeoutPromise]);
};

// Retry utility with exponential backoff for database operations
const retryWithBackoff = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: any;
  
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;
      console.log(`Database operation failed (attempt ${attempt + 1}/${maxRetries}):`, error.message);
      
      // Don't retry on authentication or permission errors
      if (error.code === 'PGRST301' || error.code === 'PGRST116' || error.message?.includes('JWT')) {
        throw error;
      }
      
      // If this is the last attempt, throw the error
      if (attempt === maxRetries - 1) {
        break;
      }
      
      // Exponential backoff: wait longer between retries
      const delay = baseDelay * Math.pow(2, attempt);
      console.log(`Retrying in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
};

export const foodLogService = {
  // Create a batch of food logs in a single meal session with local-first pattern
  async logMealSession(userId: string, foods: Omit<LoggedFood, 'id' | 'meal_session_id'>[], mealSessionId?: string, dishName?: string) {
    const sessionId = mealSessionId || uuid.v4() as string
    const loggedFoods: LoggedFood[] = []

    try {
      // LOCAL-FIRST: Save all foods to local storage immediately
      for (const foodData of foods) {
        const loggedFood: LoggedFood = {
          id: uuid.v4() as string,
          ...foodData,
          meal_session_id: sessionId,
          dish_name: dishName
        }
        
        // Save to local storage (instant)
        localStorageService.saveFoodLog(userId, loggedFood)
        loggedFoods.push(loggedFood)
        
        // Queue for cloud sync
        syncQueueService.addOperation({
          type: 'CREATE',
          userId,
          data: loggedFood
        })
      }

      // Try immediate sync to cloud (but don't wait/block if it fails)
      withSyncTimeout(
        () => this.backgroundSyncOperation(userId, sessionId),
        8000,
        'meal session sync'
      ).catch(error => {
        console.log('Background sync failed, will retry later:', error.message)
      })

      return { data: loggedFoods, error: null }
    } catch (error) {
      console.error('Error in logMealSession (local storage):', error)
      return { data: null, error }
    }
  },

  // Create food log entry (used internally for cloud sync)
  async logFood(userId: string, foodData: Omit<LoggedFood, 'id'>) {
    // Insert food log
    const { data: foodLog, error: foodError } = await supabase
      .from('food_logs')
      .insert([{
        user_id: userId,
        food_name: foodData.name,
        serving_size: foodData.serving_size,
        portion_estimate: foodData.portion_estimate,
        // NEW: Structured portion fields
        portion_count: foodData.portion_count,
        portion_description: foodData.portion_description,
        portion_weight: foodData.portion_weight,
        portion_weight_unit: foodData.portion_weight_unit,
        quantity: foodData.quantity,
        meal_type: foodData.meal_type,
        logged_at: foodData.logged_at,
        meal_session_id: foodData.meal_session_id,
        dish_name: foodData.dish_name,
      }])
      .select()
      .single()

    if (foodError) return { error: foodError }

    // Insert nutrition data
    const { error: nutritionError } = await supabase
      .from('nutrition_data')
      .insert([{
        food_log_id: foodLog.id,
        calories: foodData.calories,
        protein: foodData.protein,
        carbs: foodData.carbs,
        fat: foodData.fat,
        fiber: foodData.fiber || 0,
        sugar: foodData.sugar || 0,
        sodium: foodData.sodium || 0,
        cholesterol: foodData.cholesterol || 0,
        confidence: foodData.confidence,
      }])
      .select()
      .single()

    if (nutritionError) {
      // If nutrition insert fails, we should clean up the food log
      await supabase.from('food_logs').delete().eq('id', foodLog.id)
      return { error: nutritionError }
    }

    return { 
      data: {
        id: foodLog.id,
        ...foodData,
      }, 
      error: null 
    }
  },

  // Add a single food item to an existing meal session
  async addFoodToSession(userId: string, sessionId: string, newFoodData: Omit<LoggedFood, 'id' | 'meal_session_id'>) {
    // This reuses the logMealSession logic, providing the existing session ID
    return this.logMealSession(userId, [newFoodData], sessionId);
  },

  // Background sync operation (non-blocking)
  async backgroundSyncOperation(userId: string, _sessionId?: string) {
    try {
      const operations = syncQueueService.getReadyForRetry()
      
      for (const operation of operations) {
        try {
          switch (operation.type) {
            case 'CREATE':
              await this.logFood(userId, operation.data)
              syncQueueService.removeOperation(operation.id)
              console.log('Synced CREATE operation:', operation.id)
              break

            case 'UPDATE':
              const { foodId, updatedFood } = operation.data
              await this.syncUpdateToCloud(userId, foodId, updatedFood)
              syncQueueService.removeOperation(operation.id)
              console.log('Synced UPDATE operation:', operation.id)
              break

            case 'DELETE':
              await this.syncDeleteToCloud(userId, operation.data.foodId)
              syncQueueService.removeOperation(operation.id)
              console.log('Synced DELETE operation:', operation.id)
              break

            case 'DELETE_SESSION':
              await this.syncDeleteSessionToCloud(userId, operation.data.sessionId)
              syncQueueService.removeOperation(operation.id)
              console.log('Synced DELETE_SESSION operation:', operation.id)
              break
            case 'UPDATE_DISH_NAME':
              await this.syncUpdateDishNameToCloud(userId, operation.data.sessionId, operation.data.dishName)
              syncQueueService.removeOperation(operation.id)
              console.log('Synced UPDATE_DISH_NAME operation:', operation.id)
              break
          }
        } catch (error: any) {
          console.log('Sync failed for operation:', operation.id, error.message)
          
          // Check retry count before incrementing
          if (operation.retryCount >= 10) {
            console.log('Removing failed operation after 10 retries:', operation.id)
            syncQueueService.removeOperation(operation.id)
          } else {
            syncQueueService.incrementRetryCount(operation.id)
          }
        }
      }
    } catch (error) {
      console.log('Background sync error:', error)
    }
  },

  // Helper methods for cloud sync
  async syncUpdateToCloud(userId: string, foodId: string, updatedFood: Partial<LoggedFood>) {
    // Update food log entry
    const { error: foodError } = await supabase
      .from('food_logs')
      .update({
        food_name: updatedFood.name,
        serving_size: updatedFood.serving_size,
        portion_estimate: updatedFood.portion_estimate,
        // NEW: Structured portion fields
        portion_count: updatedFood.portion_count,
        portion_description: updatedFood.portion_description,
        portion_weight: updatedFood.portion_weight,
        portion_weight_unit: updatedFood.portion_weight_unit,
        quantity: updatedFood.quantity,
        meal_type: updatedFood.meal_type,
        dish_name: updatedFood.dish_name,
      })
      .eq('id', foodId)
      .eq('user_id', userId)

    if (foodError) throw foodError

    // Update nutrition data
    const { error: nutritionError } = await supabase
      .from('nutrition_data')
      .update({
        calories: updatedFood.calories,
        protein: updatedFood.protein,
        carbs: updatedFood.carbs,
        fat: updatedFood.fat,
        fiber: updatedFood.fiber || 0,
        sugar: updatedFood.sugar || 0,
        sodium: updatedFood.sodium || 0,
        cholesterol: updatedFood.cholesterol || 0,
        confidence: updatedFood.confidence,
      })
      .eq('food_log_id', foodId)

    if (nutritionError) throw nutritionError
  },

  async syncDeleteToCloud(userId: string, foodId: string) {
    // First delete nutrition data
    const { error: nutritionError } = await supabase
      .from('nutrition_data')
      .delete()
      .eq('food_log_id', foodId)

    if (nutritionError) throw nutritionError

    // Then delete food log
    const { error: foodError } = await supabase
      .from('food_logs')
      .delete()
      .eq('id', foodId)
      .eq('user_id', userId)

    if (foodError) throw foodError
  },

  async syncUpdateDishNameToCloud(userId: string, sessionId: string, dishName: string) {
    // Update dish name for all foods in the meal session
    const { error } = await supabase
      .from('food_logs')
      .update({ dish_name: dishName })
      .eq('meal_session_id', sessionId)
      .eq('user_id', userId)
    
    if (error) throw error
  },

  async syncDeleteSessionToCloud(userId: string, sessionId: string) {
    // Get all food logs in this session first
    const { data: foodLogs, error: getFoodLogsError } = await supabase
      .from('food_logs')
      .select('id')
      .eq('meal_session_id', sessionId)
      .eq('user_id', userId)

    if (getFoodLogsError) throw getFoodLogsError

    // Delete nutrition data for all foods in session
    if (foodLogs && foodLogs.length > 0) {
      const foodLogIds = foodLogs.map(log => log.id)
      
      const { error: nutritionError } = await supabase
        .from('nutrition_data')
        .delete()
        .in('food_log_id', foodLogIds)

      if (nutritionError) throw nutritionError

      // Delete all food logs in session
      const { error: foodError } = await supabase
        .from('food_logs')
        .delete()
        .eq('meal_session_id', sessionId)
        .eq('user_id', userId)

      if (foodError) throw foodError
    }
  },

  // Get user's food logs (local-first)
  async getFoodLogs(userId: string, date: string | null = null) {
    try {
      // Get from local storage first
      const localData = localStorageService.getFoodLogs(userId, date || undefined)
      return { data: localData, error: null }
    } catch (error) {
      console.error('Error getting local food logs:', error)
      return { data: [], error }
    }
  },

  // Get today's food logs for a user
  async getTodaysFoodLogs(userId: string) {
    const today = new Date().toISOString().split('T')[0]
    return this.getFoodLogs(userId, today)
  },

  // Get daily nutrition summary
  async getDailySummary(userId: string, date: string) {
    const { data, error } = await supabase
      .from('daily_summaries')
      .select('*')
      .eq('user_id', userId)
      .eq('date', date)
      .single()

    return { data, error }
  },

  // Get meal sessions (grouped by meal_session_id) - local-first
  async getMealSessions(userId: string, date: string | null = null): Promise<{ data: MealSession[] | null; error: any }> {
    try {
      // Get from local storage
      const mealSessions = localStorageService.getMealSessions(userId, date || undefined)
      return { data: mealSessions, error: null }
    } catch (error) {
      console.error('Error getting local meal sessions:', error)
      return { data: [], error }
    }
  },

  // Delete a single food item - local-first
  async deleteFoodItem(userId: string, foodId: string) {
    try {
      // Delete from local storage immediately
      localStorageService.deleteFoodLog(userId, foodId)

      // Queue for cloud sync
      syncQueueService.addOperation({
        type: 'DELETE',
        userId,
        data: { foodId }
      })

      // Try immediate sync (non-blocking)
      withSyncTimeout(
        () => this.backgroundSyncOperation(userId),
        8000,
        'delete food sync'
      ).catch(error => {
        console.log('Background delete sync failed, will retry later:', error.message)
      })

      return { error: null }
    } catch (error) {
      console.error('Error deleting food item locally:', error)
      return { error }
    }
  },

  // Delete entire meal session - local-first
  async deleteMealSession(userId: string, sessionId: string) {
    try {
      // Delete from local storage immediately
      localStorageService.deleteMealSession(userId, sessionId)

      // Queue for cloud sync
      syncQueueService.addOperation({
        type: 'DELETE_SESSION',
        userId,
        data: { sessionId }
      })

      // Try immediate sync (non-blocking)
      withSyncTimeout(
        () => this.backgroundSyncOperation(userId),
        8000,
        'delete session sync'
      ).catch(error => {
        console.log('Background delete session sync failed, will retry later:', error.message)
      })

      return { error: null }
    } catch (error) {
      console.error('Error deleting meal session locally:', error)
      return { error }
    }
  },

  // Update a food item - local-first
  async updateFoodItem(userId: string, foodId: string, updatedFood: Partial<LoggedFood>) {
    try {
      // Update local storage immediately
      localStorageService.updateFoodLog(userId, foodId, updatedFood)

      // Queue for cloud sync
      syncQueueService.addOperation({
        type: 'UPDATE',
        userId,
        data: { foodId, updatedFood }
      })

      // Try immediate sync (non-blocking)
      withSyncTimeout(
        () => this.backgroundSyncOperation(userId),
        8000,
        'update food sync'
      ).catch(error => {
        console.log('Background update sync failed, will retry later:', error.message)
      })

      return { error: null }
    } catch (error) {
      console.error('Error updating food item locally:', error)
      return { error }
    }
  },

  // Update dish name for all foods in a meal session - local-first
  async updateSessionDishName(userId: string, sessionId: string, dishName: string) {
    try {
      // Update local storage immediately for all foods in the session
      localStorageService.updateSessionDishName(userId, sessionId, dishName)

      // Queue for cloud sync - use a special operation type for batch dish name updates
      syncQueueService.addOperation({
        type: 'UPDATE_DISH_NAME',
        userId,
        data: { sessionId, dishName }
      })

      // Try immediate sync (non-blocking)
      withSyncTimeout(
        () => this.backgroundSyncOperation(userId),
        8000,
        'update dish name sync'
      ).catch(error => {
        console.log('Background dish name sync failed, will retry later:', error.message)
      })

      return { error: null }
    } catch (error) {
      console.error('Error updating session dish name locally:', error)
      return { error }
    }
  },

  // Update or create daily summary
  async updateDailySummary(userId: string, date: string) {
    // Get all food logs for the day
    const { data: foods, error: foodsError } = await this.getFoodLogs(userId, date)
    
    if (foodsError || !foods) return { error: foodsError }

    // Calculate totals
    const totals = foods.reduce(
      (acc, food) => ({
        total_calories: acc.total_calories + (food.calories * food.quantity),
        total_protein: acc.total_protein + (food.protein * food.quantity),
        total_carbs: acc.total_carbs + (food.carbs * food.quantity),
        total_fat: acc.total_fat + (food.fat * food.quantity),
        total_fiber: acc.total_fiber + ((food.fiber || 0) * food.quantity),
        meals_logged: acc.meals_logged + 1,
      }),
      {
        total_calories: 0,
        total_protein: 0,
        total_carbs: 0,
        total_fat: 0,
        total_fiber: 0,
        meals_logged: 0,
      }
    )

    // Upsert the daily summary
    const { data, error } = await supabase
      .from('daily_summaries')
      .upsert({
        user_id: userId,
        date: date,
        ...totals,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single()

    return { data, error }
  },

  // Sync status and manual sync methods
  getSyncStatus() {
    return syncQueueService.getSyncStatus()
  },

  async forceSyncAll(userId: string) {
    console.log('Forcing manual sync...')
    await withSyncTimeout(
      () => this.backgroundSyncOperation(userId),
      15000, // Allow longer timeout for manual sync
      'manual sync'
    )
  },

  clearSyncQueue() {
    syncQueueService.clearAll()
  },

  // Get sync queue for debugging
  getSyncQueue() {
    return syncQueueService.getOperations()
  },

  // Cleanup sync queue on app resume (called from App.tsx)
  cleanupOnAppResume() {
    syncQueueService.cleanupOnAppResume()
  }
}