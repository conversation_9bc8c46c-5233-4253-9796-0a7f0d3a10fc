import 'react-native-url-polyfill/auto'
import { createClient } from '@supabase/supabase-js'
import AsyncStorage from '@react-native-async-storage/async-storage'
import Config from 'react-native-config'

// Import processLock for thread-safe auth operations (2024 best practice)
let processLock: any
try {
  // Since supabase-js re-exports from auth-js, try importing directly from supabase-js first
  processLock = require('@supabase/supabase-js').processLock
  if (!processLock) {
    // Fallback to auth-js direct import
    processLock = require('@supabase/auth-js').processLock
  }
  console.log('ProcessLock imported successfully:', !!processLock)
} catch (error) {
  console.warn('Could not import processLock, using fallback:', error instanceof Error ? error.message : String(error))
  // Create a simple lock fallback for thread safety
  processLock = (() => {
    let locked = false
    return async (fn: () => Promise<any>) => {
      if (locked) return
      locked = true
      try {
        return await fn()
      } finally {
        locked = false
      }
    }
  })()
}

// Load configuration from environment variables
const SUPABASE_URL = Config.SUPABASE_URL || 'https://your-project.supabase.co'
const SUPABASE_ANON_KEY = Config.SUPABASE_ANON_KEY || 'your-anon-key'

if (!Config.SUPABASE_URL || !Config.SUPABASE_ANON_KEY) {
  throw new Error('Missing required environment variables: SUPABASE_URL and SUPABASE_ANON_KEY')
}

console.log('Creating Supabase client...')
console.log('URL:', SUPABASE_URL)
console.log('Key exists:', !!SUPABASE_ANON_KEY)

export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false, // Critical for React Native - we handle this manually
    lock: processLock, // Thread-safe auth operations (2024 best practice)
  },
  global: {
    headers: {
      'X-Client-Info': 'foodscanner-mobile',
    },
  },
})

// Note: AppState listener moved to AuthContext.tsx for better coordination
// This prevents race conditions between auth refresh and sync operations