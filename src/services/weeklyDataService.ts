import { MMKV } from 'react-native-mmkv'
import { supabase } from './supabase'
import { localStorageService } from './localStorageService'
import { LoggedFood } from '../types/food'

const storage = new MMKV()
const CACHED_WEEKS_KEY = 'cached_weeks'

// Week utility functions (same as in migrationService)
const getWeekStart = (date: Date): Date => {
  const d = new Date(date);
  d.setHours(0, 0, 0, 0);
  const day = d.getDay();
  const diff = d.getDate() - day;
  return new Date(d.setDate(diff));
};

const getWeekEnd = (date: Date): Date => {
  const weekStart = getWeekStart(date);
  const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);
  weekEnd.setHours(23, 59, 59, 999);
  return weekEnd;
};

const formatWeekKey = (weekStart: Date): string => {
  return weekStart.toISOString().split('T')[0]; // YYYY-MM-DD format
};

export const weeklyDataService = {
  // Check if a specific week is already cached locally
  isWeekCached(userId: string, weekStart: Date): boolean {
    const cachedWeeksKey = `${CACHED_WEEKS_KEY}_${userId}`;
    const cachedWeeksData = storage.getString(cachedWeeksKey);
    const cachedWeeks = cachedWeeksData ? JSON.parse(cachedWeeksData) : [];
    const weekKey = formatWeekKey(weekStart);
    return cachedWeeks.includes(weekKey);
  },

  // Mark a week as cached
  markWeekAsCached(userId: string, weekStart: Date): void {
    const cachedWeeksKey = `${CACHED_WEEKS_KEY}_${userId}`;
    const cachedWeeksData = storage.getString(cachedWeeksKey);
    const cachedWeeks = cachedWeeksData ? JSON.parse(cachedWeeksData) : [];
    const weekKey = formatWeekKey(weekStart);
    
    if (!cachedWeeks.includes(weekKey)) {
      cachedWeeks.push(weekKey);
      storage.set(cachedWeeksKey, JSON.stringify(cachedWeeks));
      console.log(`Marked week ${weekKey} as cached for user ${userId}`);
    }
  },

  // Get list of all cached weeks for a user
  getCachedWeeks(userId: string): string[] {
    const cachedWeeksKey = `${CACHED_WEEKS_KEY}_${userId}`;
    const cachedWeeksData = storage.getString(cachedWeeksKey);
    return cachedWeeksData ? JSON.parse(cachedWeeksData) : [];
  },

  // Download data for a specific week from Supabase
  async downloadWeekData(userId: string, weekStart: Date): Promise<{ success: boolean; data?: LoggedFood[]; error?: any }> {
    try {
      const weekEnd = getWeekEnd(weekStart);
      const weekKey = formatWeekKey(weekStart);

      console.log(`Downloading week data for ${weekKey} (${weekStart.toISOString()} to ${weekEnd.toISOString()})`);

      const { data, error } = await supabase
        .from('food_logs')
        .select(`
          id,
          food_name,
          serving_size,
          portion_estimate,
          quantity,
          meal_type,
          logged_at,
          meal_session_id,
          nutrition_data (
            calories,
            protein,
            carbs,
            fat,
            fiber,
            sugar,
            sodium,
            cholesterol,
            confidence
          )
        `)
        .eq('user_id', userId)
        .gte('logged_at', weekStart.toISOString())
        .lte('logged_at', weekEnd.toISOString())
        .order('logged_at', { ascending: false });

      if (error) {
        console.error(`Error downloading week ${weekKey}:`, error);
        return { success: false, error };
      }

      if (!data || data.length === 0) {
        console.log(`No data found for week ${weekKey}`);
        // Still mark as cached so we don't keep trying to download empty weeks
        this.markWeekAsCached(userId, weekStart);
        return { success: true, data: [] };
      }

      // Transform data to LoggedFood format
      const loggedFoods: LoggedFood[] = data.map((log: any) => ({
        id: log.id,
        name: log.food_name,
        serving_size: log.serving_size,
        portion_estimate: log.portion_estimate,
        quantity: log.quantity,
        meal_type: log.meal_type,
        logged_at: log.logged_at.endsWith('Z') ? log.logged_at : log.logged_at + 'Z',
        meal_session_id: log.meal_session_id,
        calories: log.nutrition_data?.[0]?.calories || 0,
        protein: log.nutrition_data?.[0]?.protein || 0,
        carbs: log.nutrition_data?.[0]?.carbs || 0,
        fat: log.nutrition_data?.[0]?.fat || 0,
        fiber: log.nutrition_data?.[0]?.fiber || 0,
        sugar: log.nutrition_data?.[0]?.sugar || 0,
        sodium: log.nutrition_data?.[0]?.sodium || 0,
        cholesterol: log.nutrition_data?.[0]?.cholesterol || 0,
        confidence: log.nutrition_data?.[0]?.confidence || 0,
      }));

      // Save to local storage using batch method for efficiency
      localStorageService.saveFoodLogsBatch(userId, loggedFoods);

      // Mark week as cached
      this.markWeekAsCached(userId, weekStart);

      console.log(`✅ Successfully downloaded and cached ${loggedFoods.length} food logs for week ${weekKey}`);
      return { success: true, data: loggedFoods };

    } catch (error) {
      console.error(`Failed to download week data for ${formatWeekKey(weekStart)}:`, error);
      return { success: false, error };
    }
  },

  // Download multiple weeks (useful for batch loading)
  async downloadWeeksData(userId: string, weekStarts: Date[]): Promise<{ success: boolean; downloadedWeeks: string[]; errors: any[] }> {
    const downloadedWeeks: string[] = [];
    const errors: any[] = [];

    for (const weekStart of weekStarts) {
      const result = await this.downloadWeekData(userId, weekStart);
      if (result.success) {
        downloadedWeeks.push(formatWeekKey(weekStart));
      } else {
        errors.push({ week: formatWeekKey(weekStart), error: result.error });
      }
    }

    return {
      success: errors.length === 0,
      downloadedWeeks,
      errors
    };
  },

  // Clear cached weeks info (for testing/reset)
  clearCachedWeeksInfo(userId: string): void {
    const cachedWeeksKey = `${CACHED_WEEKS_KEY}_${userId}`;
    storage.delete(cachedWeeksKey);
    console.log(`Cleared cached weeks info for user ${userId}`);
  },

  // Get weeks that need to be downloaded for a date range
  getWeeksToDownload(userId: string, startDate: Date, endDate: Date): Date[] {
    const weeksToDownload: Date[] = [];
    let currentWeekStart = getWeekStart(startDate);
    const targetWeekStart = getWeekStart(endDate);

    while (currentWeekStart <= targetWeekStart) {
      if (!this.isWeekCached(userId, currentWeekStart)) {
        weeksToDownload.push(new Date(currentWeekStart));
      }
      // Move to next week
      currentWeekStart = new Date(currentWeekStart.getTime() + 7 * 24 * 60 * 60 * 1000);
    }

    return weeksToDownload;
  },

  // Get all weeks that have data (without downloading the actual data)
  // This is used for calendar indicators - only queries for week starts, very lightweight
  async getWeeksWithData(userId: string): Promise<{ success: boolean; weeks?: string[]; error?: any }> {
    try {
      console.log('Getting weeks with data for calendar indicators...');

      const { data, error } = await supabase
        .from('food_logs')
        .select('logged_at')
        .eq('user_id', userId)
        .order('logged_at', { ascending: false });

      if (error) {
        console.error('Error getting weeks with data:', error);
        return { success: false, error };
      }

      if (!data || data.length === 0) {
        return { success: true, weeks: [] };
      }

      // Extract unique week starts
      const weeks = new Set<string>();
      data.forEach(log => {
        const sessionDate = new Date(log.logged_at);
        const weekStart = getWeekStart(sessionDate);
        weeks.add(formatWeekKey(weekStart));
      });

      console.log(`Found ${weeks.size} weeks with data`);
      return { success: true, weeks: Array.from(weeks) };

    } catch (error) {
      console.error('Failed to get weeks with data:', error);
      return { success: false, error };
    }
  }
};