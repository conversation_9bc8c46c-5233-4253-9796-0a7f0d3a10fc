import { supabase } from './supabase'
import { localStorageService } from './localStorageService'

export interface DailyInsight {
  type: 'streak' | 'progress' | 'achievement' | 'trend' | 'motivation' | 'timing' | 'variety' | 'energy'
  message: string
  value?: number
  priority: number // 1 = highest priority
}

export interface InsightsData {
  dailyNutrition: {
    total_calories: number
    total_protein: number
    total_carbs: number
    total_fat: number
    total_fiber: number
    meal_count: number
  } | null
  recentData: any[]
  insights: DailyInsight[]
}

export const insightsService = {
  // Get comprehensive insights data for today
  async getInsights(userId: string): Promise<{ data: InsightsData | null; error: any }> {
    try {
      const today = new Date().toISOString().split('T')[0]
      
      // Get today's nutrition using existing Supabase function
      const { data: dailyNutrition, error: nutritionError } = await supabase
        .rpc('calculate_daily_nutrition', {
          p_user_id: userId,
          p_date: today
        })

      if (nutritionError) {
        console.warn('⚠️ Supabase nutrition function failed:', nutritionError.message || nutritionError)
        console.log('🔄 Falling back to local calculation...')
        // Fallback to local calculation if Supabase function fails
        return this.getFallbackInsights(userId)
      }

      // Get recent 7 days data for trends using existing function
      const { data: recentData, error: recentError } = await supabase
        .rpc('get_user_recent_food_logs', {
          p_user_id: userId,
          p_days_back: 7
        })

      if (recentError) {
        console.warn('⚠️ Recent data fetch failed:', recentError.message || recentError)
      }

      // Get historical daily summaries for streak calculation
      const { data: summaries, error: summariesError } = await supabase
        .from('daily_summaries')
        .select('date, total_calories, meals_logged')
        .eq('user_id', userId)
        .gte('date', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0])
        .order('date', { ascending: false })

      if (summariesError) {
        console.warn('Error fetching summaries:', summariesError)
      }

      // Get new enhanced insights data
      let mealTimingData = null
      let varietyData = null  
      let energyData = null

      console.log('🔍 Attempting to fetch enhanced insights data...')

      try {
        console.log('📊 Calling calculate_meal_timing_insights...')
        const { data, error } = await supabase.rpc('calculate_meal_timing_insights', {
          p_user_id: userId,
          p_days_back: 7
        })
        if (error) {
          console.warn('❌ Meal timing RPC error:', error)
          console.warn('❌ Error details:', JSON.stringify(error, null, 2))
        } else {
          console.log('✅ Meal timing data:', data)
          mealTimingData = data
        }
      } catch (err: any) {
        console.warn('❌ Meal timing insights failed:', err.message || err)
      }

      try {
        console.log('📊 Calling calculate_food_variety_score...')
        const { data, error } = await supabase.rpc('calculate_food_variety_score', {
          p_user_id: userId,
          p_days_back: 7
        })
        if (error) {
          console.warn('❌ Food variety RPC error:', error)
        } else {
          console.log('✅ Food variety data:', data)
          varietyData = data
        }
      } catch (err: any) {
        console.warn('❌ Food variety insights failed:', err.message || err)
      }

      try {
        console.log('📊 Calling calculate_energy_distribution...')
        const { data, error } = await supabase.rpc('calculate_energy_distribution', {
          p_user_id: userId,
          p_days_back: 7
        })
        if (error) {
          console.warn('❌ Energy distribution RPC error:', error)
        } else {
          console.log('✅ Energy distribution data:', data)
          energyData = data
        }
      } catch (err: any) {
        console.warn('❌ Energy distribution insights failed:', err.message || err)
      }

      console.log('🎯 Enhanced insights data summary:', { 
        mealTiming: !!mealTimingData, 
        variety: !!varietyData, 
        energy: !!energyData 
      })

      // Generate insights with enhanced data
      const insights = this.generateInsights(
        dailyNutrition?.[0] || null,
        recentData || [],
        summaries || [],
        mealTimingData,
        varietyData,
        energyData
      )

      return {
        data: {
          dailyNutrition: dailyNutrition?.[0] || null,
          recentData: recentData || [],
          insights
        },
        error: null
      }

    } catch (error) {
      console.error('Error in getInsights:', error)
      return this.getFallbackInsights(userId)
    }
  },

  // Fallback to local data if Supabase functions fail
  async getFallbackInsights(userId: string): Promise<{ data: InsightsData | null; error: any }> {
    try {
      console.log('🔄 Using fallback insights calculation')
      const today = new Date().toDateString() // Match App.tsx format exactly
      const localMealSessions = localStorageService.getMealSessions(userId)
      console.log('📱 Local meal sessions found:', localMealSessions.length)
      
      // Calculate today's nutrition from meal sessions (same logic as App.tsx)
      const todaysSessions = localMealSessions.filter(session => {
        const sessionDate = new Date(session.logged_at).toDateString()
        return sessionDate === today
      })

      const todaysFoods = todaysSessions.flatMap(session => session.foods)
      console.log('📅 Today\'s sessions filtered:', todaysSessions.length)
      console.log('🍽️ Today\'s foods count:', todaysFoods.length)
      
      // Calculate basic daily nutrition from local data
      const dailyNutrition = todaysFoods.reduce(
        (acc, food) => ({
          total_calories: acc.total_calories + (food.calories * food.quantity),
          total_protein: acc.total_protein + (food.protein * food.quantity),
          total_carbs: acc.total_carbs + (food.carbs * food.quantity),
          total_fat: acc.total_fat + (food.fat * food.quantity),
          total_fiber: acc.total_fiber + ((food.fiber || 0) * food.quantity),
          meal_count: todaysSessions.length, // Count sessions, not individual foods
        }),
        {
          total_calories: 0,
          total_protein: 0,
          total_carbs: 0,
          total_fat: 0,
          total_fiber: 0,
          meal_count: 0,
        }
      )
      
      console.log('🔢 Fallback calculated daily nutrition:', { 
        calories: dailyNutrition.total_calories, 
        sessions: dailyNutrition.meal_count 
      })

      const insights = this.generateBasicInsights(dailyNutrition, todaysFoods)

      return {
        data: {
          dailyNutrition,
          recentData: [],
          insights
        },
        error: null
      }
    } catch (error) {
      console.error('Error in fallback insights:', error)
      return { data: null, error }
    }
  },

  // Generate insights based on available data
  generateInsights(
    dailyNutrition: any,
    recentData: any[],
    summaries: any[],
    mealTimingData?: any,
    varietyData?: any,
    energyData?: any
  ): DailyInsight[] {
    console.log('🔍 Generating insights with data:')
    console.log('Daily nutrition:', dailyNutrition)
    console.log('Recent data length:', recentData?.length || 0)
    console.log('Summaries length:', summaries?.length || 0)
    
    const insights: DailyInsight[] = []

    // Calculate logging streak
    const streak = this.calculateLoggingStreak(summaries)
    console.log('📈 Calculated streak:', streak)
    if (streak > 1) {
      insights.push({
        type: 'streak',
        message: `${streak} days logged in a row`,
        value: streak,
        priority: 1
      })
      console.log('✅ Added streak insight')
    }

    // Daily progress insights
    if (dailyNutrition) {
      const calories = dailyNutrition.total_calories || 0

      // Calorie progress (assuming 2000 cal goal)
      const calorieGoal = 2000
      const calorieProgress = Math.round((calories / calorieGoal) * 100)
      
      if (calorieProgress >= 80 && calorieProgress <= 120) {
        insights.push({
          type: 'progress',
          message: `intake looks balanced today`,
          value: calorieProgress,
          priority: 2
        })
      }
    }

    // Weekly trend insights from daily summaries
    if (summaries && summaries.length >= 3) {
      const recentSummaries = summaries.filter(s => s.total_calories > 0).slice(0, 7) // Last 7 days with data
      console.log('📊 Recent summaries for averaging:', recentSummaries.map(s => ({ date: s.date, calories: s.total_calories })))
      
      if (recentSummaries.length >= 3) {
        const avgCalories = Math.round(
          recentSummaries.reduce((sum, day) => sum + (day.total_calories || 0), 0) / recentSummaries.length
        )
        
        console.log('📊 Calculated average calories:', avgCalories)
        insights.push({
          type: 'trend',
          message: `averaging ${avgCalories} calories recently`,
          value: avgCalories,
          priority: 3
        })
        console.log('✅ Added trend insight')
      }
    }

    // NEW: Meal Timing Insights
    if (mealTimingData && mealTimingData.eating_window_hours) {
      const windowHours = mealTimingData.eating_window_hours
      
      if (windowHours <= 10) {
        insights.push({
          type: 'timing',
          message: `eating within ${windowHours}-hour window`,
          value: Math.round(windowHours),
          priority: 2
        })
      } else if (windowHours <= 12) {
        insights.push({
          type: 'timing', 
          message: `${windowHours}-hour eating window`,
          value: Math.round(windowHours),
          priority: 3
        })
      } else if (windowHours <= 16) {
        insights.push({
          type: 'timing',
          message: `${windowHours}-hour eating pattern`,
          value: Math.round(windowHours),
          priority: 4
        })
      }
    }

    // NEW: Food Variety Insights  
    if (varietyData && varietyData.total_unique_foods) {
      const uniqueFoods = varietyData.total_unique_foods
      const varietyScore = varietyData.variety_score || 0
      
      if (uniqueFoods >= 15) {
        insights.push({
          type: 'variety',
          message: `tried ${uniqueFoods} different foods this week`,
          value: uniqueFoods,
          priority: 2
        })
      } else if (uniqueFoods >= 8) {
        insights.push({
          type: 'variety',
          message: `good variety - ${uniqueFoods} different foods`,
          value: uniqueFoods,
          priority: 3
        })
      } else if (uniqueFoods >= 5) {
        insights.push({
          type: 'variety',
          message: `${uniqueFoods} foods tracked this week`,
          value: uniqueFoods,
          priority: 4
        })
      }
    }

    // NEW: Energy Distribution Insights
    if (energyData && energyData.morning_percentage !== undefined) {
      const morningPct = energyData.morning_percentage
      const afternoonPct = energyData.afternoon_percentage  
      const eveningPct = energyData.evening_percentage
      const frontLoaded = energyData.front_loaded
      
      if (frontLoaded && morningPct >= 25) {
        insights.push({
          type: 'energy',
          message: `${morningPct}% of calories before 2pm - energizing`,
          value: morningPct,
          priority: 2
        })
      } else if (afternoonPct >= 40) {
        insights.push({
          type: 'energy',
          message: `peak eating in afternoon - ${afternoonPct}%`,
          value: afternoonPct,
          priority: 3
        })
      } else if (eveningPct >= 50) {
        insights.push({
          type: 'energy',
          message: `evening-heavy eating - ${eveningPct}%`,
          value: eveningPct,
          priority: 4
        })
      } else if (morningPct >= 20) {
        insights.push({
          type: 'energy',
          message: `${morningPct}% morning calories`,
          value: morningPct,
          priority: 4
        })
      }
    }

    // Motivational insights
    const motivationalMessages = [
      { message: "steady progress builds lasting change", priority: 4 },
      { message: "building awareness with each meal", priority: 4 },
      { message: "consistency matters more than perfection", priority: 4 },
      { message: "tracking helps understand patterns", priority: 4 },
    ]

    // Add a motivational message if we don't have many insights
    if (insights.length < 2) {
      const randomMotivation = motivationalMessages[Math.floor(Math.random() * motivationalMessages.length)]
      insights.push({
        type: 'motivation',
        ...randomMotivation
      })
    }

    // Sort by priority and return top 3
    const finalInsights = insights
      .sort((a, b) => a.priority - b.priority)
      .slice(0, 3)
      
    console.log('🎯 Final insights generated:', finalInsights.map(i => ({ type: i.type, message: i.message })))
    return finalInsights
  },

  // Generate basic insights from local data only
  generateBasicInsights(dailyNutrition: any, foods: any[]): DailyInsight[] {
    const insights: DailyInsight[] = []

    const calories = dailyNutrition.total_calories

    if (calories > 500) {
      insights.push({
        type: 'progress',
        message: `${Math.round(calories)} calories logged today`,
        value: Math.round(calories),
        priority: 1
      })
    }

    // Add motivational message
    insights.push({
      type: 'motivation',
      message: "building healthy awareness",
      priority: 2
    })

    return insights.slice(0, 2) // Limit to 2 for basic insights
  },

  // Calculate consecutive logging streak from daily summaries
  calculateLoggingStreak(summaries: any[]): number {
    if (!summaries || summaries.length === 0) return 0

    // Sort by date descending (most recent first)
    const sortedSummaries = summaries
      .filter(s => s.meals_logged > 0)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

    if (sortedSummaries.length === 0) return 0

    let streak = 0
    let currentDate = new Date()
    currentDate.setHours(0, 0, 0, 0)

    for (const summary of sortedSummaries) {
      const summaryDate = new Date(summary.date)
      summaryDate.setHours(0, 0, 0, 0)

      const daysDiff = Math.floor((currentDate.getTime() - summaryDate.getTime()) / (1000 * 60 * 60 * 24))

      if (daysDiff === streak) {
        streak++
        currentDate.setDate(currentDate.getDate() - 1)
      } else {
        break
      }
    }

    return streak
  },

  // Get a single rotating insight (for minimal UI)
  getRotatingInsight(insights: DailyInsight[]): DailyInsight | null {
    if (insights.length === 0) return null
    
    // Rotate through insights every 7 seconds for dynamic feel
    const now = new Date()
    const totalSeconds = now.getHours() * 3600 + now.getMinutes() * 60 + now.getSeconds()
    const rotationInterval = 7 // seconds
    const index = Math.floor(totalSeconds / rotationInterval) % insights.length
    
    return insights[index]
  }
}