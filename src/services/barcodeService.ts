import { FoodItem } from './aiService';

interface OpenFoodFactsProduct {
  code: string;
  status: number;
  status_verbose: string;
  product?: {
    product_name?: string;
    product_name_en?: string;
    brands?: string;
    serving_size?: string;
    serving_quantity?: number;
    nutrition_grades?: string;
    nutriments?: {
      'energy-kcal_100g'?: number;
      'energy-kcal_serving'?: number;
      'proteins_100g'?: number;
      'proteins_serving'?: number;
      'carbohydrates_100g'?: number;
      'carbohydrates_serving'?: number;
      'fat_100g'?: number;
      'fat_serving'?: number;
      'fiber_100g'?: number;
      'fiber_serving'?: number;
      'sugars_100g'?: number;
      'sugars_serving'?: number;
      'sodium_100g'?: number;
      'sodium_serving'?: number;
      'salt_100g'?: number;
      'salt_serving'?: number;
    };
    image_url?: string;
    image_front_url?: string;
  };
}

interface BarcodeResult {
  found: boolean;
  food?: FoodItem;
  error?: string;
  source?: string;
}

interface BarcodeScanResult {
  found: boolean;
  type?: string;
  value?: string;
  confidence?: number;
  bounds?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  message?: string;
}

class BarcodeService {
  private readonly openFoodFactsBaseUrl = 'https://world.openfoodfacts.org/api/v2/product';
  
  async lookupBarcode(barcode: string): Promise<BarcodeResult> {
    console.log(`🔍 [BarcodeService] Looking up barcode: ${barcode}`);
    
    if (!barcode || barcode.trim() === '') {
      console.warn('❌ [BarcodeService] Empty barcode provided');
      return { 
        found: false, 
        error: 'Invalid barcode provided' 
      };
    }
    
    try {
      // Clean barcode - remove any non-numeric characters for most barcode types
      const cleanBarcode = barcode.replace(/[^0-9]/g, '');
      
      if (cleanBarcode.length < 6) {
        console.warn(`❌ [BarcodeService] Barcode too short: ${cleanBarcode}`);
        return { 
          found: false, 
          error: 'Barcode too short - minimum 6 digits required' 
        };
      }
      
      console.log(`📡 [BarcodeService] Fetching from Open Food Facts API: ${cleanBarcode}`);
      
      const response = await fetch(`${this.openFoodFactsBaseUrl}/${cleanBarcode}`, {
        method: 'GET',
        headers: {
          'User-Agent': 'KOA-FoodScanner/1.0 (Food tracking app)',
          'Accept': 'application/json',
        },
        timeout: 10000, // 10 second timeout
      });
      
      if (!response.ok) {
        console.warn(`⚠️ [BarcodeService] API response not OK: ${response.status}`);
        if (response.status === 404) {
          return { 
            found: false, 
            error: 'Product not found in database' 
          };
        }
        throw new Error(`API request failed with status ${response.status}`);
      }
      
      const data: OpenFoodFactsProduct = await response.json();
      console.log(`📦 [BarcodeService] API Response:`, data);
      
      if (data.status === 0 || !data.product) {
        console.log(`📭 [BarcodeService] Product not found: ${cleanBarcode}`);
        return { 
          found: false, 
          error: 'Product not found in Open Food Facts database',
          source: 'Open Food Facts'
        };
      }
      
      // Convert Open Food Facts data to our FoodItem format
      const food = this.convertToFoodItem(data.product, cleanBarcode);
      
      console.log(`✅ [BarcodeService] Successfully converted product: ${food.name}`);
      
      return {
        found: true,
        food,
        source: 'Open Food Facts'
      };
      
    } catch (error) {
      console.error('❌ [BarcodeService] API lookup failed:', error);
      
      // Provide user-friendly error messages
      if (error instanceof Error) {
        if (error.message.includes('network') || error.message.includes('fetch')) {
          return { 
            found: false, 
            error: 'Network error - please check your internet connection' 
          };
        } else if (error.message.includes('timeout')) {
          return { 
            found: false, 
            error: 'Request timed out - please try again' 
          };
        }
      }
      
      return { 
        found: false, 
        error: 'Failed to lookup product information' 
      };
    }
  }
  
  private convertToFoodItem(product: OpenFoodFactsProduct['product'], barcode: string): FoodItem {
    if (!product) {
      throw new Error('Invalid product data');
    }
    
    // Get product name (prefer English, fallback to default)
    const name = product.product_name_en || 
                 product.product_name || 
                 `Product ${barcode}`;
    
    // Get nutrition data - prefer per serving, fallback to per 100g
    const nutrients = product.nutriments || {};
    
    // Determine serving size
    let servingSize = '100g'; // default
    if (product.serving_size) {
      servingSize = product.serving_size;
    } else if (product.serving_quantity) {
      servingSize = `${product.serving_quantity}g`;
    }
    
    // Calculate nutrition values - prefer serving data, scale from 100g if needed
    const getCalories = (): number => {
      if (nutrients['energy-kcal_serving']) return nutrients['energy-kcal_serving'];
      if (nutrients['energy-kcal_100g']) return nutrients['energy-kcal_100g'];
      return 0;
    };
    
    const getProtein = (): number => {
      if (nutrients['proteins_serving']) return nutrients['proteins_serving'];
      if (nutrients['proteins_100g']) return nutrients['proteins_100g'];
      return 0;
    };
    
    const getCarbs = (): number => {
      if (nutrients['carbohydrates_serving']) return nutrients['carbohydrates_serving'];
      if (nutrients['carbohydrates_100g']) return nutrients['carbohydrates_100g'];
      return 0;
    };
    
    const getFat = (): number => {
      if (nutrients['fat_serving']) return nutrients['fat_serving'];
      if (nutrients['fat_100g']) return nutrients['fat_100g'];
      return 0;
    };
    
    const getFiber = (): number => {
      if (nutrients['fiber_serving']) return nutrients['fiber_serving'];
      if (nutrients['fiber_100g']) return nutrients['fiber_100g'];
      return 0;
    };
    
    const getSugar = (): number => {
      if (nutrients['sugars_serving']) return nutrients['sugars_serving'];
      if (nutrients['sugars_100g']) return nutrients['sugars_100g'];
      return 0;
    };
    
    const getSodium = (): number => {
      // Convert salt to sodium if needed (salt contains ~40% sodium)
      if (nutrients['sodium_serving']) return nutrients['sodium_serving'];
      if (nutrients['sodium_100g']) return nutrients['sodium_100g'];
      if (nutrients['salt_serving']) return (nutrients['salt_serving'] * 0.4);
      if (nutrients['salt_100g']) return (nutrients['salt_100g'] * 0.4);
      return 0;
    };
    
    // Create portion estimate description
    const brands = product.brands ? ` (${product.brands})` : '';
    const portionEstimate = `1 serving${brands}`;
    
    console.log(`🏷️ [BarcodeService] Converted nutrition for ${name}:`, {
      calories: getCalories(),
      protein: getProtein(),
      carbs: getCarbs(),
      fat: getFat(),
      serving: servingSize
    });
    
    return {
      id: `barcode-${barcode}-${Date.now()}`,
      name: name.toLowerCase(),
      calories: Math.round(getCalories()),
      protein: Math.round(getProtein() * 10) / 10, // 1 decimal place
      carbs: Math.round(getCarbs() * 10) / 10,
      fat: Math.round(getFat() * 10) / 10,
      fiber: Math.round(getFiber() * 10) / 10,
      sugar: Math.round(getSugar() * 10) / 10,
      sodium: Math.round(getSodium()),
      cholesterol: 0, // Not typically provided by Open Food Facts
      serving_size: servingSize,
      confidence: 0.95, // High confidence for database lookup
      portion_estimate: portionEstimate,
      lidar_enhanced: false,
    };
  }
  
  /**
   * Validate if a barcode format is likely valid for food products
   */
  isValidFoodBarcode(barcode: string, type: string): boolean {
    const cleanBarcode = barcode.replace(/[^0-9]/g, '');
    
    switch (type.toLowerCase()) {
      case 'org.gs1.ean-13':
      case 'ean13':
        return cleanBarcode.length === 13;
        
      case 'org.gs1.ean-8': 
      case 'ean8':
        return cleanBarcode.length === 8;
        
      case 'org.gs1.upce':
      case 'upce':
        return cleanBarcode.length === 6 || cleanBarcode.length === 8;
        
      case 'org.iso.code128':
      case 'code128':
        return cleanBarcode.length >= 6; // Variable length
        
      default:
        // For other types, accept if reasonable length
        return cleanBarcode.length >= 6 && cleanBarcode.length <= 20;
    }
  }
}

export const barcodeService = new BarcodeService();
export type { BarcodeResult, BarcodeScanResult };