import { MMKV } from 'react-native-mmkv'
import { LoggedFood } from '../types/food'

const storage = new MMKV()

export interface SyncOperation {
  id: string
  type: 'CREATE' | 'UPDATE' | 'DELETE' | 'DELETE_SESSION'
  userId: string
  data: any
  timestamp: number
  retryCount: number
}

export const syncQueueService = {
  // Queue operations
  addOperation(operation: Omit<SyncOperation, 'id' | 'timestamp' | 'retryCount'>): void {
    const operations = this.getOperations()
    const newOperation: SyncOperation = {
      ...operation,
      id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      retryCount: 0
    }
    
    const updatedOperations = [...operations, newOperation]
    storage.set('sync_queue', JSON.stringify(updatedOperations))
    
    console.log('Added sync operation:', newOperation.type, newOperation.id)
  },

  getOperations(): SyncOperation[] {
    const data = storage.getString('sync_queue')
    return data ? JSON.parse(data) : []
  },

  removeOperation(operationId: string): void {
    const operations = this.getOperations()
    const filteredOperations = operations.filter(op => op.id !== operationId)
    storage.set('sync_queue', JSON.stringify(filteredOperations))
    
    console.log('Removed sync operation:', operationId)
  },

  incrementRetryCount(operationId: string): void {
    const operations = this.getOperations()
    const updatedOperations = operations.map(op => 
      op.id === operationId 
        ? { ...op, retryCount: op.retryCount + 1 }
        : op
    )
    storage.set('sync_queue', JSON.stringify(updatedOperations))
  },

  // Get pending operations count
  getPendingCount(): number {
    return this.getOperations().length
  },

  // Check if sync is needed
  hasPendingOperations(): boolean {
    return this.getPendingCount() > 0
  },

  // Get operations ready for retry (with exponential backoff)
  getReadyForRetry(): SyncOperation[] {
    const operations = this.getOperations()
    const now = Date.now()
    
    return operations.filter(op => {
      if (op.retryCount === 0) return true // First attempt
      
      // Exponential backoff: 1s, 2s, 4s, 8s, max 30s
      const backoffDelay = Math.min(1000 * Math.pow(2, op.retryCount), 30000)
      const timeSinceLastAttempt = now - op.timestamp
      
      return timeSinceLastAttempt >= backoffDelay
    })
  },

  // Clear operations that are too old (stuck operations cleanup)
  clearStaleOperations(maxAgeMs: number = 300000): void { // Default 5 minutes
    const operations = this.getOperations()
    const now = Date.now()
    const nonStaleOperations = operations.filter(op => {
      const age = now - op.timestamp
      const isStale = age > maxAgeMs && op.retryCount > 5 // Old and heavily retried
      if (isStale) {
        console.log('Removing stale sync operation:', op.id, 'age:', Math.round(age / 1000) + 's')
      }
      return !isStale
    })
    
    if (nonStaleOperations.length !== operations.length) {
      storage.set('sync_queue', JSON.stringify(nonStaleOperations))
      console.log('Cleaned up', operations.length - nonStaleOperations.length, 'stale operations')
    }
  },

  // Reset retry counts on app resume (fresh start for stuck operations)
  resetRetryCountsOnAppResume(): void {
    const operations = this.getOperations()
    const resetOperations = operations.map(op => ({
      ...op,
      retryCount: Math.min(op.retryCount, 3), // Cap at 3 retries, reset higher ones
      timestamp: Date.now() // Update timestamp for fresh backoff calculation
    }))
    
    storage.set('sync_queue', JSON.stringify(resetOperations))
    console.log('Reset retry counts for app resume, queue size:', resetOperations.length)
  },

  // App resume cleanup - clear stale and reset retry counts
  cleanupOnAppResume(): void {
    console.log('Performing sync queue cleanup on app resume...')
    this.clearStaleOperations()
    this.resetRetryCountsOnAppResume()
  },

  // Clear all operations (for testing)
  clearAll(): void {
    storage.delete('sync_queue')
    console.log('Cleared all sync operations')
  },

  // Sync status
  getSyncStatus(): { isPending: boolean; pendingCount: number } {
    const pendingCount = this.getPendingCount()
    return {
      isPending: pendingCount > 0,
      pendingCount
    }
  }
}