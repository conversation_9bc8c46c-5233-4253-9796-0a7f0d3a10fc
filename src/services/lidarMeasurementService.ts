import { NativeModules } from 'react-native';
import { barcodeService, BarcodeScanResult, BarcodeResult } from './barcodeService';
import { FoodItem, RecognitionResult } from './aiService';

const { LidarMeasurementModule, BarcodeScanner } = NativeModules;

export interface MeasurementResult {
  distance: number; // in centimeters
  confidence: number; // 0-1
  quality: 'excellent' | 'good' | 'fair' | 'poor';
  measurements: {
    center: number;
    topLeft: number;
    topRight: number;
    bottomLeft: number;
    bottomRight: number;
  };
  processingTime: number; // in milliseconds
}

export interface CaptureWithDepthResult extends MeasurementResult {
  imagePath: string; // file:// path to saved JPEG
}


export interface MeasurementError {
  code: string;
  message: string;
}

class LidarMeasurementService {
  private isSessionActive: boolean = false;

  /**
   * Check if device supports LiDAR measurements
   */
  async isLidarSupported(): Promise<boolean> {
    try {
      console.log('🔍 [LidarMeasurement] All NativeModules:', Object.keys(NativeModules));
      console.log('🔍 [LidarMeasurement] LidarMeasurementModule exists:', !!LidarMeasurementModule);
      console.log('🔍 [LidarMeasurement] LidarMeasurementModule methods:', Object.keys(LidarMeasurementModule || {}));
      console.log('🔍 [LidarMeasurement] Expected methods: isLidarSupported, measureDistance, capturePhotoWithDepth');
      
      if (!LidarMeasurementModule) {
        console.error('❌ [LidarMeasurement] Module not found in NativeModules');
        return false;
      }
      
      if (typeof LidarMeasurementModule.isLidarSupported !== 'function') {
        console.error('❌ [LidarMeasurement] isLidarSupported method not available');
        return false;
      }
      
      return await LidarMeasurementModule.isLidarSupported();
    } catch (error) {
      console.error('❌ [LidarMeasurement] Error checking LiDAR support:', error);
      return false;
    }
  }

  /**
   * Perform a quick distance measurement using LiDAR
   * This will briefly start an ARKit session, take measurements, then stop
   */
  async measureDistance(): Promise<MeasurementResult> {
    console.log('📏 [LidarMeasurement] Starting distance measurement...');

    if (this.isSessionActive) {
      throw new Error('Measurement session already active');
    }

    try {
      this.isSessionActive = true;

      // Start brief ARKit session and get measurements
      const result = await LidarMeasurementModule.measureDistance();

      console.log('✅ [LidarMeasurement] Measurement complete:', {
        distance: result.distance,
        confidence: result.confidence,
        quality: result.quality
      });

      return result as MeasurementResult;
    } catch (error) {
      console.error('❌ [LidarMeasurement] Measurement failed:', error);
      throw error;
    } finally {
      this.isSessionActive = false;
      console.log('🏁 [LidarMeasurement] Session cleaned up');
    }
  }

  /**
   * Capture a photo via ARKit and return image path + depth metadata
   */
  async capturePhotoWithDepth(): Promise<CaptureWithDepthResult> {
    console.log('📸 [LidarMeasurement] Capturing photo with depth...');

    if (this.isSessionActive) {
      throw new Error('Measurement session already active');
    }

    try {
      this.isSessionActive = true;
      const result = await LidarMeasurementModule.capturePhotoWithDepth();
      if (!result?.imagePath) {
        throw new Error('No image path returned from native capture');
      }
      console.log('✅ [LidarMeasurement] Capture complete:', {
        imagePath: result.imagePath,
        distance: result.distance,
        quality: result.quality,
      });
      return result as CaptureWithDepthResult;
    } catch (error) {
      console.error('❌ [LidarMeasurement] Capture failed:', error);
      throw error;
    } finally {
      this.isSessionActive = false;
      console.log('🏁 [LidarMeasurement] Session cleaned up');
    }
  }

  /**
   * Check if a measurement session is currently active
   */
  isMeasuring(): boolean {
    return this.isSessionActive;
  }

  /**
   * Scan for barcodes using the current ARKit camera frame
   * This uses the existing AR session without interrupting LiDAR functionality
   */
  async scanBarcode(): Promise<BarcodeScanResult> {
    console.log('🔍 [BarcodeScanner] Starting barcode scan...');
    
    // Debug: Log available modules and methods
    console.log('🔍 [BarcodeScanner] Available native modules:', Object.keys(NativeModules));
    console.log('🔍 [BarcodeScanner] BarcodeScanner available methods:', Object.keys(BarcodeScanner || {}));

    try {
      if (!BarcodeScanner) {
        throw new Error('BarcodeScanner module not available');
      }
      
      if (typeof BarcodeScanner.scanBarcode !== 'function') {
        console.error('❌ [BarcodeScanner] scanBarcode method not found');
        throw new Error('scanBarcode method not available - native module needs rebuild');
      }

      // Call native barcode scanning method
      const result = await BarcodeScanner.scanBarcode();
      
      console.log('✅ [BarcodeScanner] Native scan complete:', result);
      
      return result as BarcodeScanResult;
    } catch (error) {
      console.error('❌ [BarcodeScanner] Scan failed:', error);
      throw error;
    }
  }

  /**
   * Quick check if a barcode is currently visible in the camera frame
   * Used for real-time visual feedback without capturing
   */
  async checkForBarcode(): Promise<{ found: boolean }> {
    try {
      if (!BarcodeScanner || typeof BarcodeScanner.checkForBarcode !== 'function') {
        return { found: false };
      }

      const result = await BarcodeScanner.checkForBarcode();
      return result as { found: boolean };
    } catch (error) {
      console.warn('⚠️ [BarcodeScanner] Check failed:', error);
      return { found: false };
    }
  }

  /**
   * Scan barcode and lookup nutrition data
   * This combines barcode scanning with API lookup to return food data
   */
  async scanBarcodeAndLookup(): Promise<RecognitionResult> {
    console.log('🔍 [BarcodeScanner] Starting barcode scan and lookup...');

    try {
      // First, scan for barcodes
      const scanResult = await this.scanBarcode();
      
      if (!scanResult.found || !scanResult.value) {
        console.log('📷 [BarcodeScanner] No barcode detected');
        return {
          foods: [],
          overall_confidence: 0,
          dish_name: 'No barcode detected'
        };
      }

      console.log(`🎯 [BarcodeScanner] Found barcode: ${scanResult.value} (${scanResult.type})`);

      // Validate barcode format
      if (!barcodeService.isValidFoodBarcode(scanResult.value, scanResult.type || '')) {
        console.warn(`⚠️ [BarcodeScanner] Invalid barcode format: ${scanResult.value}`);
        return {
          foods: [],
          overall_confidence: 0,
          dish_name: `Invalid barcode format: ${scanResult.value}`
        };
      }

      // Lookup nutrition data
      console.log('📡 [BarcodeScanner] Looking up nutrition data...');
      const lookupResult = await barcodeService.lookupBarcode(scanResult.value);
      
      if (!lookupResult.found || !lookupResult.food) {
        console.log(`📭 [BarcodeScanner] Product not found: ${scanResult.value}`);
        
        // Return empty result with helpful message for manual entry
        return {
          foods: [],
          overall_confidence: 0,
          dish_name: `Product not found: ${scanResult.value}`,
          error: lookupResult.error || 'Product not found in database'
        };
      }

      console.log(`✅ [BarcodeScanner] Successfully found product: ${lookupResult.food.name}`);

      // Create dish name from product info
      const dishName = lookupResult.food.name.toLowerCase();
      
      // Calculate total nutrition (same as single item since it's one product)
      const totalNutrition = {
        calories: lookupResult.food.calories,
        protein: lookupResult.food.protein,
        carbs: lookupResult.food.carbs,
        fat: lookupResult.food.fat,
        fiber: lookupResult.food.fiber,
        sugar: lookupResult.food.sugar,
        sodium: lookupResult.food.sodium,
      };

      return {
        foods: [lookupResult.food],
        overall_confidence: lookupResult.food.confidence,
        dish_name: dishName,
        total_nutrition: totalNutrition,
        source: lookupResult.source
      };

    } catch (error) {
      console.error('❌ [BarcodeScanner] Scan and lookup failed:', error);
      
      // Return error state that UI can handle
      return {
        foods: [],
        overall_confidence: 0,
        dish_name: 'Barcode scan failed',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }
}

export const lidarMeasurementService = new LidarMeasurementService();
export type { BarcodeScanResult, BarcodeResult };