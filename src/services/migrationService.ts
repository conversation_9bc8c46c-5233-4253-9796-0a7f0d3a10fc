import { MMKV } from 'react-native-mmkv'
import { supabase } from './supabase'
import { localStorageService } from './localStorageService'
import { LoggedFood } from '../types/food'

const storage = new MMKV()
const MIGRATION_KEY = 'data_migration_completed'

// Week utility functions (shared with HistoryScreen logic)
const getWeekStart = (date: Date): Date => {
  const d = new Date(date);
  d.setHours(0, 0, 0, 0);
  const day = d.getDay();
  const diff = d.getDate() - day;
  return new Date(d.setDate(diff));
};

const getWeekEnd = (date: Date): Date => {
  const weekStart = getWeekStart(date);
  const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);
  weekEnd.setHours(23, 59, 59, 999);
  return weekEnd;
};

export const migrationService = {
  async migrateUserDataIfNeeded(userId: string): Promise<boolean> {
    const migrationKey = `${MIGRATION_KEY}_${userId}`
    
    // Check if migration already completed for this user
    if (storage.getBoolean(migrationKey)) {
      console.log('Data migration already completed for user:', userId)
      return false // No migration happened
    }

    console.log('Starting optimized data migration from Supabase to local storage...')

    try {
      // Calculate date range: current week + previous week (only ~14 days of data)
      const currentWeekStart = getWeekStart(new Date());
      const previousWeekStart = new Date(currentWeekStart.getTime() - 7 * 24 * 60 * 60 * 1000);
      const currentWeekEnd = getWeekEnd(new Date());

      console.log(`Fetching recent data from ${previousWeekStart.toISOString()} to ${currentWeekEnd.toISOString()}`);

      // Fetch only recent user's food logs from Supabase (last 2 weeks)
      const { data, error } = await supabase
        .from('food_logs')
        .select(`
          id,
          food_name,
          serving_size,
          portion_estimate,
          quantity,
          meal_type,
          logged_at,
          meal_session_id,
          dish_name,
          nutrition_data (
            calories,
            protein,
            carbs,
            fat,
            fiber,
            sugar,
            sodium,
            cholesterol,
            confidence
          )
        `)
        .eq('user_id', userId)
        .gte('logged_at', previousWeekStart.toISOString())
        .lte('logged_at', currentWeekEnd.toISOString())
        .order('logged_at', { ascending: false })

      if (error) {
        console.error('Migration error fetching from Supabase:', error)
        return false // Migration failed
      }

      if (!data || data.length === 0) {
        console.log('No recent data to migrate (last 2 weeks)')
        storage.set(migrationKey, true)
        return false // No data to migrate
      }

      console.log(`Migrating ${data.length} recent food logs (last 2 weeks) to local storage...`)

      // Transform and save to local storage
      const loggedFoods: LoggedFood[] = data.map((log: any) => ({
        id: log.id,
        name: log.food_name,
        serving_size: log.serving_size,
        portion_estimate: log.portion_estimate,
        quantity: log.quantity,
        meal_type: log.meal_type,
        logged_at: log.logged_at.endsWith('Z') ? log.logged_at : log.logged_at + 'Z',
        meal_session_id: log.meal_session_id,
        dish_name: log.dish_name,
        calories: log.nutrition_data?.[0]?.calories || 0,
        protein: log.nutrition_data?.[0]?.protein || 0,
        carbs: log.nutrition_data?.[0]?.carbs || 0,
        fat: log.nutrition_data?.[0]?.fat || 0,
        fiber: log.nutrition_data?.[0]?.fiber || 0,
        sugar: log.nutrition_data?.[0]?.sugar || 0,
        sodium: log.nutrition_data?.[0]?.sodium || 0,
        cholesterol: log.nutrition_data?.[0]?.cholesterol || 0,
        confidence: log.nutrition_data?.[0]?.confidence || 0,
      }))

      // Save all foods to local storage
      localStorageService.saveFoodLogs(userId, loggedFoods)

      // Mark migration as completed
      storage.set(migrationKey, true)
      console.log(`✅ Successfully migrated ${loggedFoods.length} recent food logs (last 2 weeks) to local storage!`)
      return true // Migration successfully completed

    } catch (error) {
      console.error('Migration failed:', error)
      return false // Migration failed
    }
  },

  // Force re-migration (for testing)
  async forceMigration(userId: string): Promise<void> {
    const migrationKey = `${MIGRATION_KEY}_${userId}`
    storage.delete(migrationKey)
    await this.migrateUserDataIfNeeded(userId)
  },

  // Check if migration completed
  isMigrationCompleted(userId: string): boolean {
    const migrationKey = `${MIGRATION_KEY}_${userId}`
    return storage.getBoolean(migrationKey) || false
  }
}