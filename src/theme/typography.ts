import { Platform, TextStyle } from 'react-native';

// Font families with platform-specific fallbacks
export const FontFamilies = {
  // Headers, navigation, important UI elements - POPPINS for friendly, playful feel
  heading: Platform.select({
    ios: 'Poppins-Regular',
    android: 'Poppins-Regular',
    default: 'System',
  }),
  headingMedium: Platform.select({
    ios: 'Poppins-Medium',
    android: 'Poppins-Medium',
    default: 'System',
  }),
  headingSemiBold: Platform.select({
    ios: 'Poppins-SemiBold',
    android: 'Poppins-SemiBold',
    default: 'System',
  }),
  headingBold: Platform.select({
    ios: 'Poppins-Bold',
    android: 'Poppins-Bold',
    default: 'System',
  }),
  
  // Body text, descriptions, nutrition data - SF PRO (System) for optimal readability
  body: Platform.select({
    ios: 'System', // SF Pro on iOS
    android: 'Roboto', // Roboto on Android
    default: 'System',
  }),
  bodyMedium: Platform.select({
    ios: 'System', // SF Pro Medium on iOS
    android: 'Roboto-Medium', // Roboto Medium on Android
    default: 'System',
  }),
  bodyBold: Platform.select({
    ios: 'System', // SF Pro Bold on iOS
    android: 'Roboto-Bold', // Roboto Bold on Android
    default: 'System',
  }),
};

// Font sizes following iOS Human Interface Guidelines
export const FontSizes = {
  xs: 12,
  sm: 14,
  base: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 28,
  '4xl': 32,
  '5xl': 36,
};

// Line heights for optimal readability
export const LineHeights = {
  tight: 1.2,
  normal: 1.4,
  relaxed: 1.6,
  loose: 1.8,
};

// Typography styles for consistent use across the app
export const Typography: Record<string, TextStyle> = {
  // Headers
  h1: {
    fontFamily: FontFamilies.headingBold,
    fontSize: FontSizes['4xl'],
    lineHeight: FontSizes['4xl'] * LineHeights.tight,
    fontWeight: '700',
  },
  h2: {
    fontFamily: FontFamilies.headingSemiBold,
    fontSize: FontSizes['3xl'],
    lineHeight: FontSizes['3xl'] * LineHeights.tight,
    fontWeight: '600',
  },
  h3: {
    fontFamily: FontFamilies.headingSemiBold,
    fontSize: FontSizes['2xl'],
    lineHeight: FontSizes['2xl'] * LineHeights.tight,
    fontWeight: '600',
  },
  h4: {
    fontFamily: FontFamilies.headingMedium,
    fontSize: FontSizes.xl,
    lineHeight: FontSizes.xl * LineHeights.normal,
    fontWeight: '500',
  },
  h5: {
    fontFamily: FontFamilies.headingMedium,
    fontSize: FontSizes.lg,
    lineHeight: FontSizes.lg * LineHeights.normal,
    fontWeight: '500',
  },
  
  // Body text
  body: {
    fontFamily: FontFamilies.body,
    fontSize: FontSizes.base,
    lineHeight: FontSizes.base * LineHeights.normal,
    fontWeight: '400',
  },
  bodyLarge: {
    fontFamily: FontFamilies.body,
    fontSize: FontSizes.lg,
    lineHeight: FontSizes.lg * LineHeights.normal,
    fontWeight: '400',
  },
  bodySmall: {
    fontFamily: FontFamilies.body,
    fontSize: FontSizes.sm,
    lineHeight: FontSizes.sm * LineHeights.normal,
    fontWeight: '400',
  },
  bodyBold: {
    fontFamily: FontFamilies.bodyBold,
    fontSize: FontSizes.base,
    lineHeight: FontSizes.base * LineHeights.normal,
    fontWeight: '700',
  },
  
  // Special purpose
  caption: {
    fontFamily: FontFamilies.body,
    fontSize: FontSizes.xs,
    lineHeight: FontSizes.xs * LineHeights.normal,
    fontWeight: '400',
  },
  button: {
    fontFamily: FontFamilies.headingMedium,
    fontSize: FontSizes.base,
    lineHeight: FontSizes.base * LineHeights.tight,
    fontWeight: '500',
  },
  tabLabel: {
    fontFamily: FontFamilies.headingMedium,
    fontSize: FontSizes.sm,
    lineHeight: FontSizes.sm * LineHeights.tight,
    fontWeight: '500',
  },
  tabLabelMedium: {
    fontFamily: FontFamilies.headingMedium,
    fontSize: 17,
    lineHeight: 17 * LineHeights.tight,
    fontWeight: '500',
  },
  
  // Nutrition data specific
  nutritionValue: {
    fontFamily: FontFamilies.bodyBold,
    fontSize: FontSizes.lg,
    lineHeight: FontSizes.lg * LineHeights.tight,
    fontWeight: '700',
  },
  nutritionLabel: {
    fontFamily: FontFamilies.body,
    fontSize: FontSizes.sm,
    lineHeight: FontSizes.sm * LineHeights.normal,
    fontWeight: '400',
  },
};

// Utility function to create custom typography styles
export const createTypographyStyle = (
  family: keyof typeof FontFamilies,
  size: keyof typeof FontSizes,
  weight: TextStyle['fontWeight'] = '400'
): TextStyle => ({
  fontFamily: FontFamilies[family],
  fontSize: FontSizes[size],
  lineHeight: FontSizes[size] * LineHeights.normal,
  fontWeight: weight,
});