import * as Keychain from 'react-native-keychain'

const ENCRYPTION_KEY_SERVICE = 'koa-mmkv-encryption'
const ENCRYPTION_KEY_ACCOUNT = 'encryption-key'

/**
 * Generates a random encryption key
 */
const generateEncryptionKey = (): string => {
  // Generate a 32-byte (256-bit) encryption key
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < 64; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * Gets or creates an encryption key for MMKV
 * Stores the key securely in iOS Keychain / Android Keystore
 */
export const getOrCreateEncryptionKey = async (): Promise<string> => {
  try {
    // Try to get existing key from keychain
    const credentials = await Keychain.getInternetCredentials(ENCRYPTION_KEY_SERVICE)
    
    if (credentials && credentials.password) {
      console.log('🔑 Using existing encryption key from keychain')
      return credentials.password
    }
    
    // Generate new encryption key
    console.log('🔑 Generating new encryption key for MMKV')
    const encryptionKey = generateEncryptionKey()
    
    // Store securely in keychain/keystore
    await Keychain.setInternetCredentials(
      ENCRYPTION_KEY_SERVICE,
      ENCRYPTION_KEY_ACCOUNT,
      encryptionKey
    )
    console.log('🔑 Encryption key stored securely in keychain')
    
    return encryptionKey
  } catch (error) {
    console.error('❌ Failed to get/create encryption key:', error)
    // Fallback: use a static key (not ideal, but better than crashing)
    console.warn('⚠️ Using fallback encryption key - consider fixing keychain access')
    return 'fallback-encryption-key-change-in-production'
  }
}

/**
 * Clears the stored encryption key (for logout, etc.)
 */
export const clearEncryptionKey = async (): Promise<void> => {
  try {
    await Keychain.resetInternetCredentials(ENCRYPTION_KEY_SERVICE)
    console.log('🔑 Encryption key cleared from keychain')
  } catch (error) {
    console.error('❌ Failed to clear encryption key:', error)
  }
}