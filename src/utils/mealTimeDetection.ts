import { LoggedFood } from '../types/food';

/**
 * Automatically detects meal type based on current time
 */
export const detectMealType = (): LoggedFood['meal_type'] => {
  const now = new Date();
  const hour = now.getHours();

  // Time-based meal detection
  if (hour >= 5 && hour < 11) {
    return 'breakfast';
  } else if (hour >= 11 && hour < 16) {
    return 'lunch';
  } else if (hour >= 16 && hour < 22) {
    return 'dinner';
  } else {
    return 'snack'; // Late night or very early morning
  }
};

/**
 * Gets a friendly display name for meal types
 */
export const getMealDisplayName = (mealType: LoggedFood['meal_type']): string => {
  const displayNames = {
    breakfast: 'BREAKFAST',
    lunch: 'LUNCH', 
    dinner: 'DINNER',
    snack: 'SNACK'
  };
  
  return displayNames[mealType];
};