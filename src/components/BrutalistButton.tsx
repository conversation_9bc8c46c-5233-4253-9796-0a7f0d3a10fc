import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { BrutalistTheme } from '../theme/colors';

interface BrutalistButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'warning' | 'success';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export const BrutalistButton: React.FC<BrutalistButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  style,
  textStyle,
}) => {
  const buttonStyle = [
    styles.base,
    styles[variant],
    styles[size],
    disabled && styles.disabled,
    style,
  ];

  const buttonTextStyle = [
    styles.text,
    styles[`${variant}Text`],
    styles[`${size}Text`],
    disabled && styles.disabledText,
    textStyle,
  ];

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.8}
    >
      <Text style={buttonTextStyle}>{title.toUpperCase()}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  base: {
    borderWidth: BrutalistTheme.borderWidth.medium,
    borderColor: BrutalistTheme.colors.black,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BrutalistTheme.borderRadius.sm,
    ...BrutalistTheme.shadows.subtle,
  },

  // Variants - More elegant, less aggressive
  primary: {
    backgroundColor: BrutalistTheme.colors.black,
  },
  secondary: {
    backgroundColor: BrutalistTheme.colors.white,
    borderColor: BrutalistTheme.colors.black,
  },
  warning: {
    backgroundColor: BrutalistTheme.colors.warning,
  },
  success: {
    backgroundColor: BrutalistTheme.colors.success,
  },

  // Sizes (iOS-compliant touch targets)
  small: {
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.sm,
    minHeight: 44, // iOS minimum touch target
  },
  medium: {
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingVertical: BrutalistTheme.spacing.md,
    minHeight: 48,
  },
  large: {
    paddingHorizontal: BrutalistTheme.spacing.xl,
    paddingVertical: BrutalistTheme.spacing.lg,
    minHeight: 56,
  },

  // Disabled state
  disabled: {
    backgroundColor: BrutalistTheme.colors.grey400,
    ...BrutalistTheme.shadows.subtle,
  },

  // Text styles
  text: {
    fontWeight: '700',
    color: BrutalistTheme.colors.white,
    letterSpacing: 0.5,
    textAlign: 'center',
  },

  // Variant text colors
  primaryText: {
    color: BrutalistTheme.colors.white,
  },
  secondaryText: {
    color: BrutalistTheme.colors.black,
  },
  warningText: {
    color: BrutalistTheme.colors.black,
  },
  successText: {
    color: BrutalistTheme.colors.black,
  },

  // Size text (iOS-compliant)
  smallText: {
    fontSize: BrutalistTheme.fontSizes.callout,
  },
  mediumText: {
    fontSize: BrutalistTheme.fontSizes.headline,
  },
  largeText: {
    fontSize: BrutalistTheme.fontSizes.title3,
  },

  disabledText: {
    color: BrutalistTheme.colors.accent1,
  },
});
