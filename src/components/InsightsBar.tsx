import React, { useEffect, useRef, useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { BrutalistText } from './BrutalistText';
import { BrutalistTheme } from '../theme/colors';
import { DailyInsight } from '../services/insightsService';

interface InsightsBarProps {
  insight: DailyInsight | null;
  loading?: boolean;
  onPress?: () => void;
}

export const InsightsBar: React.FC<InsightsBarProps> = ({
  insight,
  loading = false,
  onPress,
}) => {
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const [displayedInsight, setDisplayedInsight] = useState<DailyInsight | null>(
    null,
  );

  useEffect(() => {
    if (!insight) return;

    const isFirstLoad = displayedInsight === null;
    const hasChanged = insight.message !== displayedInsight?.message;

    if (isFirstLoad) {
      setDisplayedInsight(insight);
      return;
    }

    if (hasChanged) {
      // Trigger pulse animation when insight changes
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.4,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();

      // Step 1: Slide out to left and fade out (text only)
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: -30,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start(() => {
        // Step 2: Update displayed content and position for slide in
        setDisplayedInsight(insight);
        slideAnim.setValue(30); // Position off-screen right

        // Step 3: Slide in from right and fade in (text only)
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(slideAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start();
      });
    }
  }, [insight?.message]);
  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.content}>
          <BrutalistText
            variant="caption1"
            weight="medium"
            color="grey600"
            fontFamily="mono"
            style={styles.loadingText}
          >
            LOADING INSIGHTS...
          </BrutalistText>
        </View>
      </View>
    );
  }

  if (!displayedInsight) {
    return null;
  }

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'streak':
        return 'accent1';
      case 'achievement':
        return 'success';
      case 'progress':
        return 'accent2';
      case 'trend':
        return 'secondary';
      case 'timing':
        return 'accent1'; // Orange for meal timing
      case 'variety':
        return 'success'; // Green for food variety
      case 'energy':
        return 'accent2'; // Blue for energy distribution
      case 'motivation':
        return 'grey700';
      default:
        return 'grey700';
    }
  };

  const content = (
    <View style={styles.container}>
      <View style={styles.content}>
        {displayedInsight.type !== 'motivation' && (
          <Animated.View
            style={[
              styles.indicatorLeft,
              {
                transform: [{ scale: pulseAnim }],
              },
            ]}
          >
            <View
              style={[
                styles.indicatorDot,
                {
                  backgroundColor:
                    BrutalistTheme.colors[
                      getInsightColor(displayedInsight.type)
                    ],
                },
              ]}
            />
          </Animated.View>
        )}

        <Animated.View
          style={[
            styles.insightRow,
            {
              opacity: fadeAnim,
              transform: [{ translateX: slideAnim }],
            },
          ]}
        >
          <BrutalistText
            variant="bodySmall"
            color={getInsightColor(displayedInsight.type)}
            style={styles.insightText}
          >
            {displayedInsight.message}
          </BrutalistText>
        </Animated.View>
      </View>
    </View>
  );

  if (onPress) {
    return (
      <TouchableOpacity
        onPress={onPress}
        activeOpacity={0.7}
        style={styles.touchable}
      >
        {content}
      </TouchableOpacity>
    );
  }

  return content;
};

const styles = StyleSheet.create({
  touchable: {
    width: '100%',
  },

  container: {
    backgroundColor: 'rgba(255, 255, 255, 0.96)', // Back to normal white container
    borderRadius: BrutalistTheme.borderRadius.md,
    paddingTop: BrutalistTheme.spacing.sm, // Increased from xs to sm
    paddingBottom: BrutalistTheme.spacing.sm, // Increased from xs to sm
    paddingHorizontal: BrutalistTheme.spacing.md,
    marginHorizontal: 0, // Remove horizontal margin to match other containers
    marginBottom: BrutalistTheme.spacing.sm,
    shadowColor: '#5856D6', // Subtle purple shadow
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.02,
    shadowRadius: 6,
    elevation: 1,
  },

  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  insightRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  insightText: {
    letterSpacing: 0.5,
    flex: 1,
    lineHeight: 14,
    includeFontPadding: false,
    textAlignVertical: 'center',
  },

  loadingText: {
    letterSpacing: 1,
    opacity: 0.6,
  },

  indicatorLeft: {
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: BrutalistTheme.spacing.sm,
  },

  indicatorDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
});
