import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { BrutalistText } from './BrutalistText';
import { BrutalistTheme } from '../theme/colors';
import { useAuth } from '../contexts/AuthContext';
import { CircleUserRound } from 'lucide-react-native';

interface AppHeaderProps {
  title: string;
  variant?: 'minimal' | 'standard' | 'camera';
  onProfilePress?: () => void;
}

export const AppHeader: React.FC<AppHeaderProps> = ({
  title,
  variant = 'standard',
  onProfilePress,
}) => {
  const { user } = useAuth();

  return (
    <View
      style={[
        styles.header,
        variant === 'minimal' && styles.headerMinimal,
        variant === 'camera' && styles.headerCamera,
      ]}
    >
      <BrutalistText variant="h4" color="grey700">
        {title}
      </BrutalistText>

      <Image
        source={require('../../assets/koa.png')}
        style={styles.logo}
        resizeMode="contain"
      />

      {user && onProfilePress ? (
        <TouchableOpacity
          style={styles.profileButton}
          onPress={onProfilePress}
          activeOpacity={0.7}
        >
          <CircleUserRound color={BrutalistTheme.colors.grey700} size={30} />
        </TouchableOpacity>
      ) : (
        <View style={styles.placeholder} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: 'rgba(248, 248, 252, 1)', // Match app background
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingTop: BrutalistTheme.spacing.xxs,
    paddingBottom: BrutalistTheme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(88, 86, 214, 0.06)', // Subtle purple tinted divider
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
  },
  logo: {
    width: 50,
    height: 50,
    position: 'absolute',
    left: (Dimensions.get('window').width - 50) / 2,
    top: -11,
    zIndex: 1,
  },
  placeholder: {
    width: 32,
    height: 32,
  },
  headerMinimal: {
    paddingTop: BrutalistTheme.spacing.xxs,
    paddingBottom: BrutalistTheme.spacing.sm,
  },
  headerCamera: {
    backgroundColor: 'rgba(248, 248, 252, 1)', // Match app background accent
    borderBottomWidth: 0, // No border for camera
  },
  cameraText: {
    // Same as normal text, no shadows
  },
  logoCamera: {
    // Same as normal logo, no tint
  },
  profileButton: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: BrutalistTheme.colors.accent1,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: BrutalistTheme.borderWidth.thin,
    borderColor: BrutalistTheme.colors.grey300,
  },
});
