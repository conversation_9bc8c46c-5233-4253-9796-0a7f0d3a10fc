import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { BrutalistTheme } from '../theme/colors';

interface BrutalistViewProps {
  children: React.ReactNode;
  variant?: 'card' | 'container' | 'section' | 'glass' | 'floating' | 'pill' | 'floating-attached';
  backgroundColor?: keyof typeof BrutalistTheme.colors;
  borderColor?: keyof typeof BrutalistTheme.colors;
  padding?: keyof typeof BrutalistTheme.spacing;
  margin?: keyof typeof BrutalistTheme.spacing;
  style?: ViewStyle;
}

export const BrutalistView: React.FC<BrutalistViewProps> = ({
  children,
  variant = 'container',
  backgroundColor = 'white',
  borderColor = 'black',
  padding = 'md',
  margin,
  style,
}) => {
  const viewStyle = [
    styles.base,
    styles[variant],
    {
      backgroundColor: BrutalistTheme.colors[backgroundColor],
      borderColor: BrutalistTheme.colors[borderColor],
      padding: BrutalistTheme.spacing[padding],
      ...(margin && { margin: BrutalistTheme.spacing[margin] }),
    },
    style,
  ];

  return <View style={viewStyle}>{children}</View>;
};

const styles = StyleSheet.create({
  base: {
    borderWidth: BrutalistTheme.borderWidth.medium,
  },
  
  // Original variants
  card: {
    borderWidth: BrutalistTheme.borderWidth.thick,
    borderRadius: BrutalistTheme.borderRadius.md,
    ...BrutalistTheme.shadows.strong,
  },
  container: {
    borderWidth: 0,
  },
  section: {
    borderWidth: BrutalistTheme.borderWidth.medium,
    borderRadius: BrutalistTheme.borderRadius.sm,
    marginVertical: BrutalistTheme.spacing.sm,
    ...BrutalistTheme.shadows.subtle,
  },
  
  // New glass/modern variants with temperature differentiation
  glass: {
    borderWidth: 0,
    borderRadius: BrutalistTheme.borderRadius.md,
    backgroundColor: 'rgba(248, 252, 250, 1)', // Cool green tint
    shadowColor: '#5856D6', // Subtle purple shadow
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 12,
    elevation: 3,
  },
  floating: {
    borderWidth: 0,
    borderRadius: BrutalistTheme.borderRadius.md,
    backgroundColor: 'rgba(255, 252, 248, 1)', // Warm cream tint for best contrast
    shadowColor: '#5856D6', // Subtle purple shadow
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 8,
    elevation: 2,
  },
  pill: {
    borderWidth: 0,
    borderRadius: BrutalistTheme.borderRadius.full,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    shadowColor: '#5856D6', // Subtle purple shadow
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.02,
    shadowRadius: 4,
    elevation: 1,
  },
  'floating-attached': {
    borderWidth: 0,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    borderBottomLeftRadius: BrutalistTheme.borderRadius.md,
    borderBottomRightRadius: BrutalistTheme.borderRadius.md,
    backgroundColor: 'rgba(255, 252, 248, 1)', // Warm cream tint for best contrast
    shadowColor: '#5856D6', // Subtle purple shadow
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 8,
    elevation: 2,
  },
});