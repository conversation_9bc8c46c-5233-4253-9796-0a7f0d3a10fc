import React, { useEffect } from 'react';
import { BrutalistText } from './BrutalistText';
import { useCounterAnimation } from '../hooks';

interface AnimatedConfidenceProps {
  confidence: number;
  delay?: number;
  variant?: any;
  weight?: any;
  color?: string;
  fontFamily?: any;
  style?: any;
}

export const AnimatedConfidence: React.FC<AnimatedConfidenceProps> = ({
  confidence,
  delay = 0,
  variant,
  weight,
  color,
  fontFamily,
  style,
}) => {
  const counterAnimation = useCounterAnimation({
    targetValue: confidence * 100,
    duration: 800,
    delay,
    precision: 0,
  });

  useEffect(() => {
    counterAnimation.startAnimation();
  }, []);

  return (
    <BrutalistText
      variant={variant}
      weight={weight}
      color={color}
      fontFamily={fontFamily}
      style={style}
    >
      {Math.round(counterAnimation.displayValue)}%
    </BrutalistText>
  );
};