import React from 'react'
import {
  TouchableOpacity,
  StyleSheet,
} from 'react-native'
import { BrutalistText } from './BrutalistText'
import { BrutalistTheme } from '../theme/colors'

interface DiscordSignInButtonProps {
  onPress: () => void
  disabled?: boolean
  loading?: boolean
}

export const DiscordSignInButton: React.FC<DiscordSignInButtonProps> = ({
  onPress,
  disabled = false,
  loading = false,
}) => {
  return (
    <TouchableOpacity
      style={[styles.button, disabled && styles.disabled]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      <BrutalistText
        variant="callout"
        weight="bold"
        color="white"
        fontFamily="system"
      >
        {loading ? 'Signing In...' : 'Discord'}
      </BrutalistText>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#5865F2', // Discord's brand color
    borderWidth: 2,
    borderColor: '#5865F2',
    borderRadius: BrutalistTheme.borderRadius.sm,
    minHeight: 48,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingVertical: BrutalistTheme.spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  disabled: {
    backgroundColor: BrutalistTheme.colors.grey400,
    borderColor: BrutalistTheme.colors.grey400,
  },
})