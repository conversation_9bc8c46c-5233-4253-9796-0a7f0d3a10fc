import React, { useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  Image,
  Animated,
  Easing,
} from 'react-native';
import { BrutalistText } from './BrutalistText';
import { BrutalistTheme } from '../theme/colors';

interface LoadingScreenProps {
  minimumDuration?: number; // minimum duration in milliseconds
  onMinimumDurationComplete?: () => void; // callback when minimum duration is reached
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({
  minimumDuration = 3000, // default 3 seconds
  onMinimumDurationComplete,
}) => {
  // Animation values
  const logoScale = useRef(new Animated.Value(0.8)).current;
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const loadingOpacity = useRef(new Animated.Value(0)).current;
  const breatheScale = useRef(new Animated.Value(1)).current;
  const dotScale1 = useRef(new Animated.Value(0.3)).current;
  const dotScale2 = useRef(new Animated.Value(0.3)).current;
  const dotScale3 = useRef(new Animated.Value(0.3)).current;

  // Individual letter animations for "KOA"
  const letterK = {
    scale: useRef(new Animated.Value(0.3)).current,
    opacity: useRef(new Animated.Value(0)).current,
    translateY: useRef(new Animated.Value(50)).current,
    rotate: useRef(new Animated.Value(-30)).current,
  };
  const letterO = {
    scale: useRef(new Animated.Value(0.3)).current,
    opacity: useRef(new Animated.Value(0)).current,
    translateY: useRef(new Animated.Value(50)).current,
    rotate: useRef(new Animated.Value(0)).current,
  };
  const letterA = {
    scale: useRef(new Animated.Value(0.3)).current,
    opacity: useRef(new Animated.Value(0)).current,
    translateY: useRef(new Animated.Value(50)).current,
    rotate: useRef(new Animated.Value(30)).current,
  };

  // Tagline animation
  const taglineOpacity = useRef(new Animated.Value(0)).current;
  const taglineScale = useRef(new Animated.Value(0.8)).current;
  const taglineTranslateY = useRef(new Animated.Value(20)).current;

  useEffect(() => {
    // Minimum duration timer
    const minimumDurationTimer = setTimeout(() => {
      onMinimumDurationComplete?.();
    }, minimumDuration);

    // Helper function to animate individual letters
    const animateLetter = (letter: typeof letterK, delay: number = 0) => {
      return Animated.sequence([
        Animated.delay(delay),
        Animated.parallel([
          Animated.spring(letter.scale, {
            toValue: 1,
            tension: 80,
            friction: 6,
            useNativeDriver: true,
          }),
          Animated.timing(letter.opacity, {
            toValue: 1,
            duration: 500,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: true,
          }),
          Animated.spring(letter.translateY, {
            toValue: 0,
            tension: 70,
            friction: 8,
            useNativeDriver: true,
          }),
          Animated.spring(letter.rotate, {
            toValue: 0,
            tension: 60,
            friction: 10,
            useNativeDriver: true,
          }),
        ]),
      ]);
    };

    // Initial entrance animation
    const entranceAnimation = Animated.sequence([
      // Logo entrance and KOA letters start overlapping
      Animated.parallel([
        // Logo animation
        Animated.parallel([
          Animated.timing(logoOpacity, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.spring(logoScale, {
            toValue: 1,
            tension: 50,
            friction: 8,
            useNativeDriver: true,
          }),
        ]),

        // KOA letters start after just a brief delay
        Animated.sequence([
          Animated.delay(400), // Reduced from 800ms (600+200) to 400ms
          Animated.parallel([
            animateLetter(letterK, 0),
            animateLetter(letterO, 150),
            animateLetter(letterA, 300),
          ]),
        ]),
      ]),

      // Show tagline AND loading immediately after KOA letters
      Animated.parallel([
        // Tagline animation
        Animated.timing(taglineOpacity, {
          toValue: 1,
          duration: 500,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
        Animated.spring(taglineScale, {
          toValue: 1,
          tension: 60,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.spring(taglineTranslateY, {
          toValue: 0,
          tension: 60,
          friction: 8,
          useNativeDriver: true,
        }),
        // Loading text appears at same time
        Animated.timing(loadingOpacity, {
          toValue: 1,
          duration: 500,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
      ]),
    ]);

    // Breathing animation for logo
    const breathingAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(breatheScale, {
          toValue: 1.05,
          duration: 2000,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        }),
        Animated.timing(breatheScale, {
          toValue: 1,
          duration: 2000,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        }),
      ]),
    );

    // Loading dots animation
    const dotsAnimation = Animated.loop(
      Animated.stagger(200, [
        Animated.sequence([
          Animated.timing(dotScale1, {
            toValue: 1,
            duration: 300,
            easing: Easing.out(Easing.back(1.2)),
            useNativeDriver: true,
          }),
          Animated.timing(dotScale1, {
            toValue: 0.3,
            duration: 300,
            easing: Easing.in(Easing.back(1.2)),
            useNativeDriver: true,
          }),
        ]),
        Animated.sequence([
          Animated.timing(dotScale2, {
            toValue: 1,
            duration: 300,
            easing: Easing.out(Easing.back(1.2)),
            useNativeDriver: true,
          }),
          Animated.timing(dotScale2, {
            toValue: 0.3,
            duration: 300,
            easing: Easing.in(Easing.back(1.2)),
            useNativeDriver: true,
          }),
        ]),
        Animated.sequence([
          Animated.timing(dotScale3, {
            toValue: 1,
            duration: 300,
            easing: Easing.out(Easing.back(1.2)),
            useNativeDriver: true,
          }),
          Animated.timing(dotScale3, {
            toValue: 0.3,
            duration: 300,
            easing: Easing.in(Easing.back(1.2)),
            useNativeDriver: true,
          }),
        ]),
      ]),
    );

    // Start animations
    entranceAnimation.start(() => {
      breathingAnimation.start();
      dotsAnimation.start();
    });

    // Cleanup
    return () => {
      clearTimeout(minimumDurationTimer);
      entranceAnimation.stop();
      breathingAnimation.stop();
      dotsAnimation.stop();
    };
  }, [minimumDuration, onMinimumDurationComplete]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={BrutalistTheme.colors.white}
      />

      <View style={styles.content}>
        <Animated.View
          style={[
            styles.logoContainer,
            {
              transform: [
                { scale: Animated.multiply(logoScale, breatheScale) },
              ],
              opacity: logoOpacity,
            },
          ]}
        >
          <Image
            source={require('../../assets/koa.png')}
            style={styles.logo}
            resizeMode="contain"
          />
        </Animated.View>

        <View style={styles.brandTextContainer}>
          <View style={styles.lettersContainer}>
            <Animated.View
              style={{
                transform: [
                  { scale: letterK.scale },
                  { translateY: letterK.translateY },
                  {
                    rotateZ: letterK.rotate.interpolate({
                      inputRange: [-360, 360],
                      outputRange: ['-360deg', '360deg'],
                    }),
                  },
                ],
                opacity: letterK.opacity,
              }}
            >
              <BrutalistText
                variant="brutalistHero"
                color="black"
                style={[styles.brandText, { fontWeight: '200' }]}
              >
                K
              </BrutalistText>
            </Animated.View>

            <Animated.View
              style={{
                transform: [
                  { scale: letterO.scale },
                  { translateY: letterO.translateY },
                  {
                    rotateZ: letterO.rotate.interpolate({
                      inputRange: [-360, 360],
                      outputRange: ['-360deg', '360deg'],
                    }),
                  },
                ],
                opacity: letterO.opacity,
              }}
            >
              <BrutalistText
                variant="brutalistHero"
                color="black"
                style={[styles.brandText, { fontWeight: '200' }]}
              >
                O
              </BrutalistText>
            </Animated.View>

            <Animated.View
              style={{
                transform: [
                  { scale: letterA.scale },
                  { translateY: letterA.translateY },
                  {
                    rotateZ: letterA.rotate.interpolate({
                      inputRange: [-360, 360],
                      outputRange: ['-360deg', '360deg'],
                    }),
                  },
                ],
                opacity: letterA.opacity,
              }}
            >
              <BrutalistText
                variant="brutalistHero"
                color="black"
                style={[styles.brandText, { fontWeight: '200' }]}
              >
                A
              </BrutalistText>
            </Animated.View>
          </View>
        </View>

        <Animated.View
          style={[
            styles.taglineContainer,
            {
              opacity: taglineOpacity,
              transform: [
                { scale: taglineScale },
                { translateY: taglineTranslateY },
              ],
            },
          ]}
        >
          <BrutalistText
            variant="body"
            color="grey700"
            style={styles.taglineText}
          >
            know our appetite
          </BrutalistText>
        </Animated.View>
      </View>

      {/* Loading at bottom */}
      <Animated.View
        style={[styles.bottomLoadingContainer, { opacity: loadingOpacity }]}
      >
        <BrutalistText variant="h5" color="grey600" style={styles.loadingText}>
          Loading
        </BrutalistText>

        <View style={styles.dotsContainer}>
          <Animated.View
            style={[styles.dot, { transform: [{ scale: dotScale1 }] }]}
          />
          <Animated.View
            style={[styles.dot, { transform: [{ scale: dotScale2 }] }]}
          />
          <Animated.View
            style={[styles.dot, { transform: [{ scale: dotScale3 }] }]}
          />
        </View>
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.white,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: BrutalistTheme.spacing.lg,
    paddingBottom: 80, // Move everything up by adding bottom padding
  },
  logoContainer: {
    marginBottom: BrutalistTheme.spacing.sm,
  },
  logo: {
    width: 80,
    height: 80,
  },
  brandTextContainer: {
    marginBottom: BrutalistTheme.spacing.md,
  },
  lettersContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  brandText: {
    textAlign: 'center',
  },
  taglineContainer: {
    marginBottom: BrutalistTheme.spacing.lg,
  },
  taglineText: {
    textAlign: 'center',
    fontStyle: 'italic',
    letterSpacing: 1.5,
    fontSize: 16,
    fontWeight: '500',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomLoadingContainer: {
    position: 'absolute',
    bottom: 60,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    textAlign: 'center',
    marginRight: BrutalistTheme.spacing.sm,
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: BrutalistTheme.colors.grey600,
  },
});
