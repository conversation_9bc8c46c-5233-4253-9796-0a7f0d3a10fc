import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  ScrollView,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { BrutalistText } from './BrutalistText';
import { BrutalistTheme } from '../theme/colors';
import { useAuth } from '../contexts/AuthContext';

interface ProfileModalProps {
  visible: boolean;
  onClose: () => void;
}

export const ProfileModal: React.FC<ProfileModalProps> = ({
  visible,
  onClose,
}) => {
  const { user, profile, signOut, updateProfile, deleteAccount } = useAuth();
  
  // Local state for editing values (like MealEditModal)
  const [localGoals, setLocalGoals] = useState({
    calories: 2000,
    protein: 150,
    carbs: 250,
    fat: 65
  });
  
  const [localDisplayName, setLocalDisplayName] = useState('');

  // Track if we've loaded actual profile data
  const [hasLoadedProfile, setHasLoadedProfile] = useState(false);
  // Track if we're currently saving to prevent resetting during profile reload
  const [isSaving, setIsSaving] = useState(false);

  // Update local state when modal becomes visible
  useEffect(() => {
    if (visible && !isSaving) {
      if (profile) {
        console.log('🎯 ProfileModal: Loading profile data:', {
          calories: profile.daily_calorie_goal,
          protein: profile.daily_protein_goal,
          carbs: profile.daily_carb_goal,
          fat: profile.daily_fat_goal
        });
        setLocalGoals({
          calories: profile.daily_calorie_goal ?? 2000,
          protein: profile.daily_protein_goal ?? 150,
          carbs: profile.daily_carb_goal ?? 250,
          fat: profile.daily_fat_goal ?? 65
        });
        setLocalDisplayName(profile.full_name ?? profile.username ?? '');
        setHasLoadedProfile(true);
      } else {
        // Allow setting goals even without profile data (for new accounts or offline)
        console.log('🎯 ProfileModal: No profile data, using defaults for new account');
        setLocalGoals({
          calories: 2000,
          protein: 150,
          carbs: 250,
          fat: 65
        });
        setLocalDisplayName('');
        setHasLoadedProfile(true);
      }
    }
  }, [visible, profile, isSaving]);

  const handleLogout = () => {
    Alert.alert(
      'SIGN OUT',
      'Are you sure you want to sign out of your account?',
      [
        {
          text: 'CANCEL',
          style: 'cancel',
        },
        {
          text: 'SIGN OUT',
          style: 'destructive',
          onPress: async () => {
            try {
              // Cancel any pending saves and close modal immediately for better UX
              setIsSaving(false);
              onClose();
              
              const { error } = await signOut();
              if (error) {
                Alert.alert('Error', 'Failed to sign out. Please try again.');
              }
            } catch (err) {
              Alert.alert('Error', 'Failed to sign out. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'DELETE ACCOUNT',
      'Are you sure you want to permanently delete your account?\n\nThis will:\n• Delete all your food logs and data\n• Remove your profile completely\n• Cannot be undone\n\nType DELETE to confirm.',
      [
        {
          text: 'CANCEL',
          style: 'cancel',
        },
        {
          text: 'DELETE ACCOUNT',
          style: 'destructive',
          onPress: () => {
            // Show confirmation prompt
            Alert.prompt(
              'CONFIRM DELETION',
              'Type "DELETE" to permanently delete your account:',
              [
                {
                  text: 'CANCEL',
                  style: 'cancel',
                },
                {
                  text: 'DELETE',
                  style: 'destructive',
                  onPress: async (text) => {
                    if (text?.toUpperCase() === 'DELETE') {
                      try {
                        const { error } = await deleteAccount();
                        if (error) {
                          Alert.alert('Error', 'Failed to delete account. Please try again.');
                        } else {
                          Alert.alert('Account Deleted', 'Your account has been permanently deleted.');
                          onClose();
                        }
                      } catch (err) {
                        Alert.alert('Error', 'Failed to delete account. Please try again.');
                      }
                    } else {
                      Alert.alert('Deletion Cancelled', 'You must type "DELETE" to confirm.');
                    }
                  },
                },
              ],
              'plain-text'
            );
          },
        },
      ]
    );
  };

  // Get user initials for large avatar
  const getInitials = () => {
    if (profile?.full_name) {
      return profile.full_name
        .split(' ')
        .map(name => name.charAt(0).toUpperCase())
        .slice(0, 2)
        .join('');
    }
    if (profile?.username) {
      return profile.username.charAt(0).toUpperCase();
    }
    if (user?.email) {
      return user.email.charAt(0).toUpperCase();
    }
    return '?';
  };

  const updateLocalGoal = (goalType: 'calories' | 'protein' | 'carbs' | 'fat', value: string) => {
    const numValue = parseInt(value) || 0;
    setLocalGoals(prev => ({ ...prev, [goalType]: numValue }));
  };

  const saveGoalsToDatabase = async () => {
    // Don't save if we haven't loaded the actual profile data yet
    if (!hasLoadedProfile) {
      console.log('🎯 ProfileModal: Skipping save - profile not loaded yet');
      return;
    }
    
    // Don't save if user is not logged in (prevents race conditions during logout)
    if (!user) {
      console.log('🎯 ProfileModal: Skipping save - user not logged in');
      return;
    }
    
    try {
      setIsSaving(true);
      console.log('🎯 ProfileModal: Saving goals to database:', localGoals);
      const result = await updateProfile({
        full_name: localDisplayName.trim(),
        daily_calorie_goal: localGoals.calories,
        daily_protein_goal: localGoals.protein,
        daily_carb_goal: localGoals.carbs,
        daily_fat_goal: localGoals.fat
      });
      console.log('🎯 ProfileModal: updateProfile result:', result);
    } catch (error) {
      console.error('🎯 ProfileModal: Save goals error:', error);
      Alert.alert('Error', 'Failed to save changes. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveAndClose = async () => {
    // No need to show an alert on success, just save and close.
    // The context update will re-render the parent screen with fresh data.
    try {
      await saveGoalsToDatabase();
    } catch (error) {
      // The saveGoalsToDatabase function already shows an alert on error.
      console.error('Failed to save goals on close:', error);
    }
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleSaveAndClose}
    >
      <KeyboardAvoidingView 
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <SafeAreaView style={styles.modalContainer}>
        <View style={styles.header}>
          <BrutalistText
            variant="title3"
            fontFamily="system"
            weight="black"
            color="grey800"
          >
            ACCOUNT
          </BrutalistText>
          
          <TouchableOpacity 
            style={styles.closeButton}
            onPress={handleSaveAndClose}
            activeOpacity={0.7}
          >
            <BrutalistText
              variant="callout"
              weight="medium"
              color="grey600"
              fontFamily="mono"
            >
              DONE
            </BrutalistText>
          </TouchableOpacity>
        </View>

        <ScrollView 
          style={styles.content} 
          showsVerticalScrollIndicator={false}
        >
          {/* Profile Section */}
          <View style={styles.profileSection}>
            <View style={styles.largeAvatar}>
              <BrutalistText
                variant="title1"
                weight="bold"
                fontFamily="system"
                color="white"
              >
                {getInitials()}
              </BrutalistText>
            </View>

            <View style={styles.userInfo}>
              <TextInput
                style={[
                  styles.displayNameInput,
                  !localDisplayName && styles.displayNameInputEmpty
                ]}
                value={localDisplayName}
                onChangeText={setLocalDisplayName}
                placeholder="set name"
                placeholderTextColor={BrutalistTheme.colors.grey500}
                autoCapitalize="words"
                returnKeyType="done"
              />
              
              <BrutalistText
                variant="callout"
                color="grey600"
                fontFamily="mono"
              >
                {user?.email}
              </BrutalistText>
            </View>
          </View>

          {/* Daily Goals Section - Clean like Today screen */}
          <View style={styles.section}>
            <View style={styles.sectionHeaderContainer}>
              <BrutalistText
                variant="caption1"
                weight="medium"
                color="grey700"
                fontFamily="mono"
                style={styles.sectionHeader}
              >
                DAILY NUTRITION GOALS
              </BrutalistText>
              <BrutalistText
                variant="caption2"
                color="grey500"
                fontFamily="mono"
                style={styles.editHint}
              >
                TAP TO EDIT
              </BrutalistText>
            </View>

            <View style={styles.goalsContainer}>
              <View style={styles.goalRow}>
                <BrutalistText variant="callout" color="grey600">Calories</BrutalistText>
                <TextInput
                  style={styles.goalValue}
                  value={localGoals.calories.toString()}
                  onChangeText={(text) => updateLocalGoal('calories', text)}
                  keyboardType="numeric"
                  returnKeyType="done"
                />
              </View>
              
              <View style={styles.goalRow}>
                <BrutalistText variant="callout" color="grey600">Protein</BrutalistText>
                <View style={styles.goalWithUnit}>
                  <TextInput
                    style={styles.goalValue}
                    value={localGoals.protein.toString()}
                    onChangeText={(text) => updateLocalGoal('protein', text)}
                    keyboardType="numeric"
                    returnKeyType="done"
                  />
                  <BrutalistText style={styles.unitText}>g</BrutalistText>
                </View>
              </View>
              
              <View style={styles.goalRow}>
                <BrutalistText variant="callout" color="grey600">Carbs</BrutalistText>
                <View style={styles.goalWithUnit}>
                  <TextInput
                    style={styles.goalValue}
                    value={localGoals.carbs.toString()}
                    onChangeText={(text) => updateLocalGoal('carbs', text)}
                    keyboardType="numeric"
                    returnKeyType="done"
                  />
                  <BrutalistText style={styles.unitText}>g</BrutalistText>
                </View>
              </View>
              
              <View style={styles.goalRow}>
                <BrutalistText variant="callout" color="grey600">Fat</BrutalistText>
                <View style={styles.goalWithUnit}>
                  <TextInput
                    style={styles.goalValue}
                    value={localGoals.fat.toString()}
                    onChangeText={(text) => updateLocalGoal('fat', text)}
                    keyboardType="numeric"
                    returnKeyType="done"
                  />
                  <BrutalistText style={styles.unitText}>g</BrutalistText>
                </View>
              </View>
            </View>
          </View>

          {/* Account Section */}
          <View style={styles.section}>
            <BrutalistText
              variant="caption1"
              weight="medium"
              color="grey700"
              fontFamily="mono"
              style={styles.sectionHeader}
            >
              ACCOUNT DETAILS
            </BrutalistText>

            <View style={styles.accountInfo}>
              <View style={styles.accountRow}>
                <BrutalistText
                  variant="callout"
                  color="grey600"
                >
                  Member since
                </BrutalistText>
                <BrutalistText
                  variant="callout"
                  weight="medium"
                  color="grey800"
                  fontFamily="mono"
                >
                  {profile?.created_at 
                    ? new Date(profile.created_at).toLocaleDateString()
                    : 'N/A'
                  }
                </BrutalistText>
              </View>

              <View style={styles.accountRow}>
                <BrutalistText
                  variant="callout"
                  color="grey600"
                >
                  Account ID
                </BrutalistText>
                <BrutalistText
                  variant="caption1"
                  weight="medium"
                  color="grey700"
                  fontFamily="mono"
                >
                  {user?.id?.slice(0, 8)}...
                </BrutalistText>
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Account Actions */}
        <View style={styles.footer}>
          <TouchableOpacity 
            style={styles.logoutButton}
            onPress={handleLogout}
            activeOpacity={0.7}
          >
            <BrutalistText
              variant="callout"
              weight="medium"
              color="grey600"
              fontFamily="mono"
            >
              Sign out
            </BrutalistText>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.deleteButton}
            onPress={handleDeleteAccount}
            activeOpacity={0.7}
          >
            <BrutalistText
              variant="caption2"
              weight="medium"
              style={{ color: '#FF9999' }}
              fontFamily="mono"
            >
              Delete account
            </BrutalistText>
          </TouchableOpacity>
        </View>
        </SafeAreaView>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(248, 248, 252, 1)', // Match app background
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(248, 248, 252, 1)', // Match app background
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingTop: BrutalistTheme.spacing.md,
    paddingBottom: BrutalistTheme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(88, 86, 214, 0.06)', // Subtle purple tinted divider
  },
  closeButton: {
    position: 'absolute',
    right: BrutalistTheme.spacing.lg,
  },
  content: {
    flex: 1,
    paddingHorizontal: BrutalistTheme.spacing.lg,
  },
  
  // Profile Section
  profileSection: {
    alignItems: 'center',
    paddingVertical: BrutalistTheme.spacing.xl,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(88, 86, 214, 0.06)', // Subtle purple tinted divider
    marginBottom: BrutalistTheme.spacing.lg,
  },
  largeAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: BrutalistTheme.colors.accent1,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: BrutalistTheme.borderWidth.medium,
    borderColor: BrutalistTheme.colors.grey300,
    marginBottom: BrutalistTheme.spacing.md,
  },
  userInfo: {
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xxs,
  },
  userName: {
    textAlign: 'center',
  },
  displayNameInput: {
    fontSize: BrutalistTheme.fontSizes.title3,
    fontWeight: 'bold',
    fontFamily: BrutalistTheme.fonts.system,
    color: BrutalistTheme.colors.grey900,
    backgroundColor: 'transparent',
    borderWidth: 0,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(88, 86, 214, 0.2)', // Purple tinted border
    padding: BrutalistTheme.spacing.xs,
    textAlign: 'center',
    marginBottom: BrutalistTheme.spacing.xs,
  },
  displayNameInputEmpty: {
    fontSize: BrutalistTheme.fontSizes.callout,
    fontWeight: 'normal',
  },

  // Sections
  section: {
    marginBottom: BrutalistTheme.spacing.xl,
  },
  sectionHeaderContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: BrutalistTheme.spacing.md,
  },
  sectionHeader: {
    letterSpacing: 1,
  },
  editHint: {
    letterSpacing: 0.5,
    opacity: 0.7,
  },

  // Goals Container - Clean like History screen
  goalsContainer: {
    gap: 0,
  },
  goalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: BrutalistTheme.spacing.xs,
    paddingHorizontal: BrutalistTheme.spacing.md,
    borderBottomWidth: BrutalistTheme.borderWidth.hairline,
    borderBottomColor: 'rgba(88, 86, 214, 0.06)', // Subtle purple tinted divider
  },
  goalValue: {
    fontSize: BrutalistTheme.fontSizes.title3,
    fontWeight: 'bold',
    fontFamily: BrutalistTheme.fonts.mono,
    color: BrutalistTheme.colors.accent1,
    backgroundColor: 'transparent',
    borderWidth: 0,
    padding: 0,
    textAlign: 'right',
    minWidth: 40,
  },
  goalWithUnit: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  unitText: {
    fontSize: BrutalistTheme.fontSizes.title3,
    fontWeight: 'bold',
    fontFamily: BrutalistTheme.fonts.mono,
    color: BrutalistTheme.colors.accent1,
  },


  // Account Info
  accountInfo: {
    gap: BrutalistTheme.spacing.sm,
  },
  accountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: BrutalistTheme.spacing.xs,
    borderBottomWidth: BrutalistTheme.borderWidth.hairline,
    borderBottomColor: 'rgba(88, 86, 214, 0.06)', // Subtle purple tinted divider
  },

  // Footer
  footer: {
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingTop: BrutalistTheme.spacing.md,
    paddingBottom: BrutalistTheme.spacing.lg,
    borderTopWidth: 1,
    borderTopColor: 'rgba(88, 86, 214, 0.06)', // Subtle purple tinted divider
  },
  logoutButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: BrutalistTheme.spacing.md,
    paddingHorizontal: BrutalistTheme.spacing.lg,
  },
  deleteButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: BrutalistTheme.spacing.xs,
    paddingHorizontal: BrutalistTheme.spacing.md,
    marginTop: BrutalistTheme.spacing.xs,
  },
});