import React, { useRef, useCallback, useImperativeHandle, forwardRef } from 'react';
import {
  View,
  PanResponder,
  Animated,
  Dimensions,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import HapticFeedback from 'react-native-haptic-feedback';
import { BrutalistText } from './BrutalistText';
import { BrutalistTheme } from '../theme/colors';

interface SwipeableItemProps {
  children: React.ReactNode;
  onDelete: () => void;
  onEdit?: () => void;
  deleteText?: string;
  editText?: string;
  disabled?: boolean;
  deleteThreshold?: number;
  scrollEnabled?: boolean;
  onSwipeStart?: (id: string) => void;
  onSwipeReset?: () => void;
  itemId?: string;
}

export interface SwipeableItemRef {
  reset: () => void;
}

const { width: screenWidth } = Dimensions.get('window');
const ACTION_ZONE_WIDTH = 100;
const SWIPE_THRESHOLD = 60;
const HAPTIC_THRESHOLD = 40;

export const SwipeableItem = forwardRef<SwipeableItemRef, SwipeableItemProps>(({
  children,
  onDelete,
  onEdit,
  deleteText = 'DELETE',
  editText = 'EDIT',
  disabled = false,
  deleteThreshold = SWIPE_THRESHOLD,
  scrollEnabled = true,
  onSwipeStart,
  onSwipeReset,
  itemId = Math.random().toString(),
}, ref) => {
  const translateX = useRef(new Animated.Value(0)).current;
  const lastOffset = useRef(0);
  const hasTriggeredHaptic = useRef(false);
  const isActivelyGesturing = useRef(false);
  const isCurrentlyOpen = useRef(false);


  const resetPosition = useCallback(() => {
    Animated.spring(translateX, {
      toValue: 0,
      useNativeDriver: true,
      tension: 150,
      friction: 8,
    }).start(() => {
      // Only call onSwipeReset after animation completes
      if (onSwipeReset && isCurrentlyOpen.current) {
        onSwipeReset();
      }
    });
    lastOffset.current = 0;
    hasTriggeredHaptic.current = false;
    isCurrentlyOpen.current = false;
  }, [translateX, onSwipeReset]);

  useImperativeHandle(ref, () => ({
    reset: resetPosition,
  }), [resetPosition]);

  const handleDelete = useCallback(() => {
    // Animate item sliding out completely before calling onDelete
    Animated.timing(translateX, {
      toValue: -screenWidth,
      duration: 250,
      useNativeDriver: true,
    }).start(() => {
      onDelete();
      // Reset position for potential reuse
      translateX.setValue(0);
      lastOffset.current = 0;
      hasTriggeredHaptic.current = false;
    });
  }, [translateX, onDelete]);

  const handleEdit = useCallback(() => {
    if (onEdit) {
      onEdit();
      // Reset position after edit is triggered
      resetPosition();
    }
  }, [onEdit, resetPosition]);

  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: (_, gestureState) => {
      // Only respond to horizontal swipes with clear horizontal intent
      const isHorizontal = Math.abs(gestureState.dx) > Math.abs(gestureState.dy);
      const hasMinimumMovement = Math.abs(gestureState.dx) > 10;
      const isNotVerticalScroll = Math.abs(gestureState.dy) < 20;
      
      return !disabled && isHorizontal && hasMinimumMovement && isNotVerticalScroll;
    },
    onMoveShouldSetPanResponderCapture: (_, gestureState) => {
      // Be more aggressive about capturing horizontal swipes
      const isDefinitelyHorizontal = Math.abs(gestureState.dx) > Math.abs(gestureState.dy) * 2;
      const hasSignificantMovement = Math.abs(gestureState.dx) > 15;
      
      return !disabled && isDefinitelyHorizontal && hasSignificantMovement;
    },
    onPanResponderGrant: () => {
      // Set offset for smooth continuation
      isActivelyGesturing.current = true;
      translateX.setOffset(lastOffset.current);
      translateX.setValue(0);
    },
    onPanResponderMove: (_, gestureState) => {
      if (disabled) return;

      // Allow both directions but limit range
      const maxRight = onEdit ? ACTION_ZONE_WIDTH : 0;
      const maxLeft = -ACTION_ZONE_WIDTH;
      const newValue = Math.max(maxLeft, Math.min(maxRight, gestureState.dx));
      translateX.setValue(newValue);


      // Trigger haptic feedback at threshold
      if (Math.abs(newValue) > HAPTIC_THRESHOLD && !hasTriggeredHaptic.current) {
        HapticFeedback.trigger('impactLight');
        hasTriggeredHaptic.current = true;
      } else if (Math.abs(newValue) <= HAPTIC_THRESHOLD) {
        hasTriggeredHaptic.current = false;
      }
    },
    onPanResponderRelease: (_, gestureState) => {
      isActivelyGesturing.current = false;
      if (disabled) return;

      const finalOffset = lastOffset.current + gestureState.dx;
      const velocity = gestureState.vx;
      const absOffset = Math.abs(finalOffset);

      translateX.flattenOffset();

      // Consider velocity for faster swipes
      const shouldTriggerAction = absOffset > deleteThreshold || 
                                (absOffset > deleteThreshold * 0.6 && Math.abs(velocity) > 0.5);

      if (shouldTriggerAction) {
        if (finalOffset > 0 && onEdit) {
          // Swipe right - edit action
          Animated.spring(translateX, {
            toValue: ACTION_ZONE_WIDTH,
            useNativeDriver: true,
            tension: 150,
            friction: 8,
          }).start();
          lastOffset.current = ACTION_ZONE_WIDTH;
        } else if (finalOffset < 0) {
          // Swipe left - delete action
          Animated.spring(translateX, {
            toValue: -ACTION_ZONE_WIDTH,
            useNativeDriver: true,
            tension: 150,
            friction: 8,
          }).start();
          lastOffset.current = -ACTION_ZONE_WIDTH;
        } else {
          resetPosition();
          return;
        }
        
        // Only notify if we weren't already open
        if (!isCurrentlyOpen.current && onSwipeStart) {
          onSwipeStart(itemId);
        }
        isCurrentlyOpen.current = true;
        HapticFeedback.trigger('impactMedium');
      } else {
        // Not far enough - snap back
        resetPosition();
      }
    },
    onPanResponderTerminationRequest: () => {
      // Allow termination if we're in the middle of a swipe but not committed
      return Math.abs(lastOffset.current) < deleteThreshold * 0.5;
    },
    onPanResponderTerminate: () => {
      // Handle termination gracefully - reset position
      isActivelyGesturing.current = false;
      resetPosition();
    },
  });

  const deleteZoneOpacity = translateX.interpolate({
    inputRange: [-ACTION_ZONE_WIDTH, -HAPTIC_THRESHOLD, 0],
    outputRange: [1, 0.8, 0],
    extrapolate: 'clamp',
  });

  const deleteZoneScale = translateX.interpolate({
    inputRange: [-ACTION_ZONE_WIDTH, -HAPTIC_THRESHOLD, 0],
    outputRange: [1, 0.9, 0.8],
    extrapolate: 'clamp',
  });

  const editZoneOpacity = translateX.interpolate({
    inputRange: [0, HAPTIC_THRESHOLD, ACTION_ZONE_WIDTH],
    outputRange: [0, 0.8, 1],
    extrapolate: 'clamp',
  });

  const editZoneScale = translateX.interpolate({
    inputRange: [0, HAPTIC_THRESHOLD, ACTION_ZONE_WIDTH],
    outputRange: [0.8, 0.9, 1],
    extrapolate: 'clamp',
  });

  return (
    <View style={styles.container}>
      {/* Edit Zone - Left Side */}
      {onEdit && (
        <Animated.View 
          style={[
            styles.editZone,
            {
              opacity: editZoneOpacity,
              transform: [{ scaleX: editZoneScale }],
            },
          ]}
        >
          <TouchableOpacity
            style={styles.editButton}
            onPress={handleEdit}
            activeOpacity={0.8}
          >
            <BrutalistText
              variant="callout"
              weight="bold"
              color="white"
              fontFamily="system"
              style={styles.actionText}
            >
              {editText}
            </BrutalistText>
          </TouchableOpacity>
        </Animated.View>
      )}

      {/* Delete Zone - Right Side */}
      <Animated.View 
        style={[
          styles.deleteZone,
          {
            opacity: deleteZoneOpacity,
            transform: [{ scaleX: deleteZoneScale }],
          },
        ]}
      >
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={handleDelete}
          activeOpacity={0.8}
        >
          <BrutalistText
            variant="callout"
            weight="bold"
            color="white"
            fontFamily="mono"
            style={styles.actionText}
          >
            {deleteText}
          </BrutalistText>
        </TouchableOpacity>
      </Animated.View>

      {/* Main Content */}
      <Animated.View
        style={[
          styles.content,
          {
            transform: [{ translateX }],
          },
        ]}
        {...panResponder.panHandlers}
      >
        {children}
      </Animated.View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
  },
  content: {
    backgroundColor: 'transparent',
    zIndex: 2,
  },
  editZone: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    width: ACTION_ZONE_WIDTH,
    backgroundColor: BrutalistTheme.colors.accent2 || '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  deleteZone: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    width: ACTION_ZONE_WIDTH,
    backgroundColor: BrutalistTheme.colors.error || '#dc3545',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  editButton: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: BrutalistTheme.spacing.sm,
  },
  deleteButton: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: BrutalistTheme.spacing.sm,
  },
  actionText: {
    textAlign: 'center',
    letterSpacing: 1,
  },
});