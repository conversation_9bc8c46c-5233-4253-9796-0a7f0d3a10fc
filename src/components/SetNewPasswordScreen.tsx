import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TextInput,
  Alert,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { BrutalistText } from './index';
import { BrutalistTheme } from '../theme/colors';
import { useAuth } from '../contexts/AuthContext';

interface SetNewPasswordScreenProps {
  onComplete: () => void;
}

export const SetNewPasswordScreen: React.FC<SetNewPasswordScreenProps> = ({
  onComplete,
}) => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { updatePassword } = useAuth();

  const validateForm = () => {
    if (password.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters');
      return false;
    }
    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return false;
    }
    return true;
  };

  const handleUpdatePassword = async () => {
    if (!validateForm()) return;

    console.log('Starting password update...');
    setIsLoading(true);
    
    try {
      const { error } = await updatePassword(password);
      console.log('Password update result:', { error });

      if (error) {
        console.error('Password update error:', error);
        Alert.alert('Update Failed', error.message || 'Unknown error occurred');
        setIsLoading(false);
      } else {
        console.log('Password updated successfully');
        Alert.alert(
          'Password Updated!',
          'Your password has been successfully updated.',
          [{ text: 'OK', onPress: onComplete }]
        );
        setIsLoading(false);
      }
    } catch (err) {
      console.error('Password update exception:', err);
      Alert.alert('Update Failed', 'An unexpected error occurred. Please try again.');
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={BrutalistTheme.colors.white}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={styles.keyboardAvoid}
      >
        <View style={styles.content}>
          {/* Top Section */}
          <View style={styles.topSection}>
            <View style={styles.header}>
              <BrutalistText
                variant="title1"
                fontFamily="system"
                weight="black"
                color="grey800"
              >
                KOA
              </BrutalistText>
              <BrutalistText
                variant="body"
                fontFamily="serif"
                weight="regular"
                color="grey700"
                style={styles.expandedSubtitle}
              >
                know our appetite
              </BrutalistText>
            </View>

            {/* Form Section Title */}
            <View style={styles.formTitleContainer}>
              <BrutalistText
                variant="caption1"
                fontFamily="mono"
                weight="medium"
                color="grey600"
                style={styles.formTitle}
              >
                SET NEW PASSWORD
              </BrutalistText>
            </View>

            <View style={styles.form}>
            <BrutalistText
              variant="body"
              fontFamily="system"
              weight="regular"
              color="grey700"
              style={styles.description}
            >
              Enter your new password below.
            </BrutalistText>

            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={password}
                onChangeText={setPassword}
                placeholder="New password"
                placeholderTextColor={BrutalistTheme.colors.grey500}
                secureTextEntry
                returnKeyType="next"
              />
            </View>

            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                placeholder="Confirm new password"
                placeholderTextColor={BrutalistTheme.colors.grey500}
                secureTextEntry
                returnKeyType="go"
                onSubmitEditing={handleUpdatePassword}
              />
            </View>

            <TouchableOpacity
              style={[
                styles.updateButton,
                isLoading && styles.updateButtonDisabled
              ]}
              onPress={handleUpdatePassword}
              disabled={isLoading}
              activeOpacity={0.7}
            >
              <BrutalistText
                variant="callout"
                weight="bold"
                color="white"
                fontFamily="system"
              >
                {isLoading ? 'Updating...' : 'Update Password'}
              </BrutalistText>
            </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.white,
  },
  keyboardAvoid: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingVertical: BrutalistTheme.spacing.lg,
    justifyContent: 'space-between',
  },

  // Top section with header and form
  topSection: {
    justifyContent: 'flex-start',
    paddingTop: BrutalistTheme.spacing.xl,
  },

  // Header
  header: {
    alignItems: 'center',
    marginBottom: BrutalistTheme.spacing.xxl,
    gap: BrutalistTheme.spacing.xs,
  },
  expandedSubtitle: {
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: BrutalistTheme.spacing.xs,
  },

  // Form section title
  formTitleContainer: {
    marginBottom: BrutalistTheme.spacing.md,
  },
  formTitle: {
    letterSpacing: 1,
    textAlign: 'left',
  },

  // Form
  form: {
    gap: BrutalistTheme.spacing.md,
  },
  description: {
    textAlign: 'center',
    marginBottom: BrutalistTheme.spacing.lg,
    lineHeight: 22,
  },
  inputContainer: {
    marginBottom: BrutalistTheme.spacing.sm,
  },
  input: {
    borderBottomWidth: 2,
    borderBottomColor: BrutalistTheme.colors.grey300,
    paddingHorizontal: 0,
    paddingVertical: BrutalistTheme.spacing.md,
    fontSize: BrutalistTheme.fontSizes.body,
    fontFamily: BrutalistTheme.fonts.system,
    backgroundColor: BrutalistTheme.colors.white,
    color: BrutalistTheme.colors.grey800,
  },
  updateButton: {
    backgroundColor: BrutalistTheme.colors.black,
    paddingVertical: BrutalistTheme.spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: BrutalistTheme.spacing.lg,
    borderWidth: 1,
    borderColor: BrutalistTheme.colors.black,
    borderRadius: BrutalistTheme.borderRadius.sm,
  },
  updateButtonDisabled: {
    backgroundColor: BrutalistTheme.colors.grey400,
    borderColor: BrutalistTheme.colors.grey400,
  },
});