import React, { useEffect, useRef, useState } from 'react';
import { View, StyleSheet, Animated, StatusBar, TouchableOpacity } from 'react-native';
import { BrutalistText } from './BrutalistText';
import { BrutalistTheme } from '../theme/colors';

interface AnalyzingScreenProps {
  onComplete?: (result?: any) => void;
  result?: { foods: any[] } | null;
  onRetry?: () => void;
}

export const AnalyzingScreen: React.FC<AnalyzingScreenProps> = ({
  onComplete,
  result = null,
  onRetry,
}) => {
  const circleAnim = useRef(new Animated.Value(0)).current;
  const innerCircleAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const textFadeAnim = useRef(new Animated.Value(0)).current;
  const textSlideAnim = useRef(new Animated.Value(20)).current;
  const messageOpacityAnim = useRef(new Animated.Value(1)).current;
  const [elapsedTime, setElapsedTime] = useState(0);
  const [currentMessage, setCurrentMessage] = useState("Uploading...");
  const [messageIndex, setMessageIndex] = useState(0);
  
  // Determine if we should show no results state
  const showNoResults = result !== null && result.foods.length === 0;

  useEffect(() => {
    // Outer circle rotation animation (clockwise)
    const circleRotation = Animated.loop(
      Animated.timing(circleAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      }),
    );

    // Inner circle rotation animation (counter-clockwise, faster)
    const innerCircleRotation = Animated.loop(
      Animated.timing(innerCircleAnim, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      }),
    );

    // Start both rotations immediately
    circleRotation.start();
    innerCircleRotation.start();

    // Staggered sequence using Animated.sequence for better control
    const staggeredSequence = Animated.sequence([
      // Initial delay (increased to 400ms)
      Animated.delay(400),
      
      // Spinner/circle fade and scale in
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]),
      
      // Additional delay before text
      Animated.delay(300),
      
      // Text animations
      Animated.parallel([
        Animated.timing(textFadeAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(textSlideAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }),
      ]),
    ]);

    // Start the sequence
    staggeredSequence.start();

    return () => {
      circleRotation.stop();
      innerCircleRotation.stop();
      staggeredSequence.stop();
    };
  }, []);

  useEffect(() => {
    // Timer for elapsed time and message rotation
    const interval = setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    // Rotate messages every few seconds with animation
    const messages = [
      "Uploading...",
      'Analyzing your food...',
      'Getting nutritional data...',
      'Almost ready...',
    ];

    // Different durations for each message
    const messageDurations = [4000, 2500, 2500, 2500]; // Uploading lasts 4 seconds

    let timeoutId: NodeJS.Timeout;

    const scheduleNextMessage = (currentIndex: number) => {
      if (currentIndex < messages.length - 1) {
        timeoutId = setTimeout(() => {
          // Fade out current message
          Animated.timing(messageOpacityAnim, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }).start(() => {
            const nextIndex = currentIndex + 1;
            setMessageIndex(nextIndex);
            setCurrentMessage(messages[nextIndex]);
            
            // Fade in new message
            Animated.timing(messageOpacityAnim, {
              toValue: 1,
              duration: 300,
              useNativeDriver: true,
            }).start();

            // Schedule next message if not at the end
            scheduleNextMessage(nextIndex);
          });
        }, messageDurations[currentIndex]);
      }
    };

    // Start the sequence
    scheduleNextMessage(0);

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, []);

  // Handle result completion
  useEffect(() => {
    if (result !== null && result.foods.length > 0 && onComplete) {
      // We have valid results - trigger completion
      onComplete(result);
    }
  }, [result, onComplete]);

  const rotate = circleAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const innerRotate = innerCircleAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['360deg', '0deg'], // Counter-clockwise
  });

  // No results state
  if (showNoResults) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="white" />
        
        <View style={styles.noResultsContent}>
          {/* Question mark icon */}
          <View style={styles.noResultsIcon}>
            <View style={styles.noResultsCircle}>
              <BrutalistText
                variant="title1"
                weight="black"
                color="grey600"
                fontFamily="system"
                style={styles.questionMark}
              >
                ?
              </BrutalistText>
            </View>
          </View>
          
          {/* Main message */}
          <View style={styles.noResultsTextContainer}>
            <BrutalistText
              variant="title2"
              weight="black"
              color="black"
              fontFamily="system"
              style={styles.noResultsTitle}
            >
              No food detected
            </BrutalistText>
            
            <BrutalistText
              variant="body"
              weight="medium"
              color="grey600"
              fontFamily="system"
              style={styles.noResultsSubtitle}
            >
              We couldn't identify any food items in your photo. Try getting a clearer shot with better lighting.
            </BrutalistText>
          </View>
          
          {/* Try again button */}
          <TouchableOpacity
            onPress={onRetry}
            style={styles.retryButton}
            activeOpacity={0.7}
          >
            <BrutalistText
              variant="callout"
              weight="medium"
              color="black"
              fontFamily="system"
              style={styles.retryButtonText}
            >
              Try Again
            </BrutalistText>
            <View style={styles.retryButtonUnderline} />
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Regular analyzing state
  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        {/* Main Circle Loader */}
        <View style={styles.loaderContainer}>
          <Animated.View
            style={[styles.spinnerRing, { transform: [{ rotate }] }]}
          />
          <Animated.View
            style={[styles.innerSpinnerRing, { transform: [{ rotate: innerRotate }] }]}
          />
          <View style={styles.timerDisplay}>
            <BrutalistText
              variant="body"
              weight="black"
              color="black"
              fontFamily="mono"
              style={styles.timerInCircle}
            >
              {elapsedTime}
            </BrutalistText>
          </View>
        </View>

        {/* Text Content */}
        <Animated.View 
          style={[
            styles.textContainer,
            {
              opacity: textFadeAnim,
              transform: [{ translateY: textSlideAnim }],
            },
          ]}
        >
          <Animated.View style={{ opacity: messageOpacityAnim }}>
            <BrutalistText
              variant="title2"
              weight="black"
              color="black"
              fontFamily="system"
              style={styles.mainText}
            >
              {currentMessage}
            </BrutalistText>
          </Animated.View>

        </Animated.View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(248, 248, 252, 1)', // Match app background
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: BrutalistTheme.spacing.lg,
  },
  content: {
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xxl * 1.2,
    maxWidth: 320,
  },
  loaderContainer: {
    width: 120,
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  spinnerRing: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 4,
    borderColor: 'rgba(88, 86, 214, 0.1)', // Subtle purple tint
    borderTopColor: 'rgba(88, 86, 214, 0.6)', // Purple accent
    borderRightColor: 'rgba(88, 86, 214, 0.6)', // Purple accent
    position: 'absolute',
  },
  innerSpinnerRing: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 3,
    borderColor: 'rgba(88, 86, 214, 0.15)', // Subtle purple tint
    borderBottomColor: 'rgba(88, 86, 214, 0.4)', // Purple accent
    borderLeftColor: 'rgba(88, 86, 214, 0.4)', // Purple accent
    position: 'absolute',
  },
  timerDisplay: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  timerInCircle: {
    letterSpacing: 1,
    fontSize: 20,
    lineHeight: 20,
  },
  textContainer: {
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xl,
    width: '100%',
    paddingHorizontal: BrutalistTheme.spacing.md,
    minHeight: 80, // Fixed height to prevent layout shifts
  },
  mainText: {
    letterSpacing: 1.5,
    textAlign: 'center',
    lineHeight: 24,
    width: '100%',
    fontSize: 18,
    minHeight: 48, // Fixed height for 2 lines of text
  },
  tipText: {
    letterSpacing: 1.5,
    textAlign: 'center',
    marginTop: BrutalistTheme.spacing.md,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    opacity: 0.7,
  },
  
  // No results styles
  noResultsContent: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: BrutalistTheme.spacing.lg,
    gap: BrutalistTheme.spacing.xl,
    maxWidth: 320,
  },
  noResultsIcon: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: BrutalistTheme.spacing.md,
  },
  noResultsCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: 'rgba(88, 86, 214, 0.2)', // Purple tinted border
    backgroundColor: 'rgba(248, 248, 252, 0.8)', // Subtle purple background
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#5856D6',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },
  questionMark: {
    fontSize: 32,
    lineHeight: 32,
  },
  noResultsTextContainer: {
    alignItems: 'center',
    gap: BrutalistTheme.spacing.md,
  },
  noResultsTitle: {
    letterSpacing: 1,
    textAlign: 'center',
    fontSize: 20,
  },
  noResultsSubtitle: {
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: BrutalistTheme.spacing.md,
  },
  retryButton: {
    alignItems: 'center',
    paddingVertical: BrutalistTheme.spacing.sm,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    marginTop: BrutalistTheme.spacing.lg,
  },
  retryButtonText: {
    letterSpacing: 0.5,
  },
  retryButtonUnderline: {
    height: 2,
    backgroundColor: BrutalistTheme.colors.black,
    marginTop: 2,
    width: '100%',
  },
});
