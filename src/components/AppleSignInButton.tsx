import React from 'react'
import {
  TouchableOpacity,
  StyleSheet,
} from 'react-native'
import { BrutalistText } from './BrutalistText'
import { BrutalistTheme } from '../theme/colors'

interface AppleSignInButtonProps {
  onPress: () => void
  disabled?: boolean
  loading?: boolean
}

export const AppleSignInButton: React.FC<AppleSignInButtonProps> = ({
  onPress,
  disabled = false,
  loading = false,
}) => {
  return (
    <TouchableOpacity
      style={[styles.button, disabled && styles.disabled]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
    >
      <BrutalistText
        variant="callout"
        weight="bold"
        color="white"
        fontFamily="system"
      >
        {loading ? 'Signing In...' : 'Apple'}
      </BrutalistText>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  button: {
    backgroundColor: BrutalistTheme.colors.black,
    borderWidth: 2,
    borderColor: BrutalistTheme.colors.black,
    borderRadius: BrutalistTheme.borderRadius.sm,
    minHeight: 48,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingVertical: BrutalistTheme.spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  disabled: {
    backgroundColor: BrutalistTheme.colors.grey400,
    borderColor: BrutalistTheme.colors.grey400,
  },
})