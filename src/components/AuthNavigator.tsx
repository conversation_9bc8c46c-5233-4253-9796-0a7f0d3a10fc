import React, { useState } from 'react'
import { LoginScreen } from './LoginScreen'
import { SignupScreen } from './SignupScreen'
import { SmsAuthScreen } from './SmsAuthScreen'
import { ForgotPasswordScreen } from './ForgotPasswordScreen'

type AuthScreenType = 'login' | 'signup' | 'sms' | 'forgot-password'

export const AuthNavigator: React.FC = () => {
  const [currentScreen, setCurrentScreen] = useState<AuthScreenType>('login')

  switch (currentScreen) {
    case 'login':
      return (
        <LoginScreen 
          onSwitchToSignup={() => setCurrentScreen('signup')}
          onSwitchToSms={() => setCurrentScreen('sms')}
          onSwitchToForgotPassword={() => setCurrentScreen('forgot-password')}
        />
      )
    case 'signup':
      return (
        <SignupScreen 
          onSwitchToLogin={() => setCurrentScreen('login')}
          onSwitchToSms={() => setCurrentScreen('sms')}
        />
      )
    case 'sms':
      return (
        <SmsAuthScreen 
          onBack={() => setCurrentScreen('login')}
        />
      )
    case 'forgot-password':
      return (
        <ForgotPasswordScreen 
          onBack={() => setCurrentScreen('login')}
        />
      )
    default:
      return (
        <LoginScreen 
          onSwitchToSignup={() => setCurrentScreen('signup')}
          onSwitchToSms={() => setCurrentScreen('sms')}
          onSwitchToForgotPassword={() => setCurrentScreen('forgot-password')}
        />
      )
  }
}