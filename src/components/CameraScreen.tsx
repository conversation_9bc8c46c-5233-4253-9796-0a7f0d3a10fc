import React, { useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  ActivityIndicator,
  TouchableOpacity,
  Platform,
  StatusBar,
} from 'react-native';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import { BrutalistButton, BrutalistText, BrutalistView } from './index';
import { AnalyzingScreen } from './AnalyzingScreen';
import { BrutalistTheme } from '../theme/colors';
import { aiService, RecognitionResult } from '../services/aiService';
import { lidarMeasurementService } from '../services/lidarMeasurementService';
import { ARPreviewView } from '../native/ARPreviewView';
import { Barcode, TextSearch, ScanEye } from 'lucide-react-native';

interface CameraScreenProps {
  onFoodRecognized: (result: RecognitionResult) => void;
  onSearchRequested?: () => void;
}

export const CameraScreen: React.FC<CameraScreenProps> = ({
  onFoodRecognized,
  onSearchRequested,
}) => {
  const [cameraAuthorized, setCameraAuthorized] = useState<boolean | null>(
    null,
  );
  const [isProcessing, setIsProcessing] = useState(false);
  const [scanMode, setScanMode] = useState<'ai' | 'barcode'>('ai');
  const [barcodeDetected, setBarcodeDetected] = useState(false);
  const [processingResult, setProcessingResult] =
    useState<RecognitionResult | null>(null);

  React.useEffect(() => {
    console.log('📱 [CameraScreen] Component mounted');
    checkCameraPermission();
  }, []);

  // Auto-scanning for barcode mode
  React.useEffect(() => {
    console.log('🔄 [CameraScreen] Barcode effect triggered', {
      scanMode,
      cameraAuthorized,
      isProcessing,
    });
    let interval: NodeJS.Timeout | null = null;

    if (scanMode === 'barcode' && cameraAuthorized && !isProcessing) {
      console.log('🔍 [CameraScreen] Starting barcode detection');

      interval = setInterval(async () => {
        console.log('⏰ [CameraScreen] Auto-scan attempt');
        try {
          // Just attempt a full scan - if no barcode is found, it'll return empty
          const result = await lidarMeasurementService.scanBarcodeAndLookup();

          if (result.foods.length > 0) {
            console.log('🎯 [CameraScreen] Auto-scan found product!');
            setBarcodeDetected(true);
            onFoodRecognized(result);
          } else if (result.error?.includes('Product not found')) {
            console.log(
              '📦 [CameraScreen] Auto-scan found barcode but no product data',
            );
            setBarcodeDetected(true);
            onFoodRecognized(result);
          } else {
            setBarcodeDetected(false);
          }
        } catch (error) {
          // This is expected when no barcode is visible
          setBarcodeDetected(false);
        }
      }, 1500); // Check every 1.5 seconds for responsive UX
    } else {
      console.log(
        '❌ [CameraScreen] Not starting detection - conditions not met',
      );
      setBarcodeDetected(false);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [scanMode, cameraAuthorized, isProcessing, onFoodRecognized]);

  const checkCameraPermission = async () => {
    if (Platform.OS !== 'ios') {
      setCameraAuthorized(true);
      return;
    }
    try {
      const status = await check(PERMISSIONS.IOS.CAMERA);
      if (status === RESULTS.GRANTED) {
        setCameraAuthorized(true);
      } else {
        const req = await request(PERMISSIONS.IOS.CAMERA);
        setCameraAuthorized(req === RESULTS.GRANTED);
      }
    } catch (e) {
      console.warn('Camera permission check failed', e);
      setCameraAuthorized(false);
    }
  };

  const takePhoto = useCallback(async () => {
    console.log('📸 [CameraScreen] Starting photo capture');
    setIsProcessing(true);

    try {
      // iOS: Capture a still image with ARKit + LiDAR depth
      const captureResult =
        await lidarMeasurementService.capturePhotoWithDepth();
      console.log('✅ [CameraScreen] Photo captured with depth');

      const imageUri = captureResult.imagePath;

      // Step 3: Analyze with AI (including measurement data if available)
      console.log('🤖 [CameraScreen] Starting AI analysis...');
      let result = await aiService.recognizeFood(imageUri, captureResult);

      console.log('✅ [CameraScreen] AI analysis complete');
      setProcessingResult(result);
      // Let AnalyzingScreen handle the result first
      // onFoodRecognized(result);
    } catch (error) {
      console.error('❌ [CameraScreen] Photo processing error:', error);

      // Show specific error messages based on the error type
      let errorMessage = 'Failed to analyze food. Please try again.';
      if (error instanceof Error) {
        if (error.message.includes('API key')) {
          errorMessage = 'API key not configured. Please check your setup.';
        } else if (
          error.message.includes('Rate limit') ||
          error.message.includes('quota')
        ) {
          errorMessage =
            'Please add credits to your OpenAI account to continue.';
        } else if (error.message.includes('clearer image')) {
          errorMessage =
            'Could not analyze the image. Please try a clearer photo with better lighting.';
        }
      }

      // iOS fix: delay alert to avoid modal/spinner conflicts
      setTimeout(() => {
        Alert.alert('Food Analysis Failed', errorMessage, [
          { text: 'Try Again', style: 'default' },
        ]);
      }, 500);
    } finally {
      setIsProcessing(false);
      console.log('🏁 [CameraScreen] Photo processing complete');
    }
  }, []);

  const toggleBarcodeMode = useCallback(() => {
    setScanMode(scanMode === 'barcode' ? 'ai' : 'barcode');
  }, [scanMode]);

  if (cameraAuthorized === false) {
    return (
      <BrutalistView style={styles.permissionContainer}>
        <BrutalistText variant="title2" style={styles.permissionText}>
          Camera Permission Required
        </BrutalistText>
        <BrutalistText style={styles.permissionDescription}>
          Please allow camera access to scan food items
        </BrutalistText>
        <BrutalistButton
          title="Request Permission"
          onPress={checkCameraPermission}
          style={styles.permissionButton}
        />
      </BrutalistView>
    );
  }

  if (cameraAuthorized === null) {
    return (
      <BrutalistView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={BrutalistTheme.colors.primary} />
        <BrutalistText style={styles.loadingText}>Preparing...</BrutalistText>
      </BrutalistView>
    );
  }

  // Handle completion from AnalyzingScreen
  const handleAnalyzingComplete = (result: RecognitionResult) => {
    setProcessingResult(null);
    setIsProcessing(false);
    onFoodRecognized(result);
  };

  const handleRetryFromNoResults = () => {
    setProcessingResult(null);
    setIsProcessing(false);
  };

  // Show analyzing screen when processing or when we have a result to handle
  if (isProcessing || processingResult) {
    return (
      <AnalyzingScreen
        result={processingResult}
        onComplete={handleAnalyzingComplete}
        onRetry={handleRetryFromNoResults}
      />
    );
  }

  return (
    <View style={styles.container}>
      {/* AR Camera Preview with LiDAR */}
      <ARPreviewView style={styles.camera} />
      <View style={styles.overlay}>
        <View style={styles.scanArea}>
          {/* Barcode Scanning Overlay */}
          {scanMode === 'barcode' && (
            <View style={styles.barcodeScanningOverlay}>
              <View
                style={[
                  styles.barcodeFrame,
                  barcodeDetected && styles.barcodeFrameActive,
                ]}
              />
              <BrutalistText
                variant="bodySmall"
                color="white"
                style={styles.barcodeInstructions}
              >
                {barcodeDetected ? 'barcode found!' : 'point at barcode'}
              </BrutalistText>
              {barcodeDetected && <View style={styles.detectionIndicator} />}
            </View>
          )}
        </View>

        <View style={styles.controls}>
          <View style={styles.unifiedButtonContainer}>
            {/* Barcode Toggle Button */}
            <TouchableOpacity
              onPress={toggleBarcodeMode}
              style={[
                styles.barcodeToggleExtension,
                scanMode === 'barcode' && styles.barcodeToggleActive,
              ]}
              activeOpacity={0.7}
            >
              <Barcode
                color={scanMode === 'barcode' ? '#000000' : '#FFFFFF'}
                size={30}
              />
            </TouchableOpacity>

            {/* Main Scan Button - Always AI Photo */}
            <TouchableOpacity
              onPress={takePhoto}
              style={styles.mainScanButton}
              activeOpacity={0.7}
            >
              <ScanEye color="#FFFFFF" size={30} />
            </TouchableOpacity>

            {/* Search Extension */}
            {onSearchRequested && (
              <TouchableOpacity
                onPress={onSearchRequested}
                style={styles.manualAddExtension}
                activeOpacity={0.7}
              >
                <TextSearch color="#FFFFFF" size={30} />
              </TouchableOpacity>
            )}
          </View>

          {/* Floating Labels Below */}
          <View style={styles.floatingLabels}>
            <BrutalistText
              variant="caption"
              color="white"
              style={styles.floatingLabel}
            >
              barcode
            </BrutalistText>
            <BrutalistText
              variant="caption"
              color="white"
              style={styles.floatingLabel}
            >
              scan
            </BrutalistText>
            {onSearchRequested && (
              <BrutalistText
                variant="caption"
                color="white"
                style={styles.floatingLabel}
              >
                search
              </BrutalistText>
            )}
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.black,
  },
  camera: {
    flex: 1,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
    paddingVertical: BrutalistTheme.spacing.xl,
    paddingHorizontal: BrutalistTheme.spacing.md,
  },
  scanArea: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  barcodeScanningOverlay: {
    alignItems: 'center',
    gap: BrutalistTheme.spacing.lg,
  },
  barcodeFrame: {
    width: 250,
    height: 80,
    borderWidth: 2,
    borderColor: BrutalistTheme.colors.white,
    borderRadius: 8,
    borderStyle: 'dashed',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  barcodeFrameActive: {
    borderColor: BrutalistTheme.colors.accent1,
    backgroundColor: 'rgba(255, 255, 0, 0.2)',
    borderStyle: 'solid',
  },
  barcodeInstructions: {
    opacity: 0.8,
    letterSpacing: 1,
    textAlign: 'center',
  },
  detectionIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: BrutalistTheme.colors.accent1,
    marginTop: BrutalistTheme.spacing.xs,
  },
  controls: {
    alignItems: 'center',
    paddingBottom: BrutalistTheme.spacing.md,
    gap: BrutalistTheme.spacing.sm,
  },
  unifiedButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: BrutalistTheme.borderWidth.thin,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    borderRadius: 50,
    overflow: 'hidden',
    gap: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  mainScanButton: {
    width: 85,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanButtonInner: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: BrutalistTheme.colors.white,
    opacity: 0.9,
    justifyContent: 'center',
    alignItems: 'center',
  },
  barcodeToggleExtension: {
    width: 75,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderTopLeftRadius: 50,
    borderBottomLeftRadius: 50,
    paddingLeft: 8,
  },
  barcodeToggleActive: {
    backgroundColor: BrutalistTheme.colors.accent1,
  },
  manualAddExtension: {
    width: 75,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderTopRightRadius: 50,
    borderBottomRightRadius: 50,
    paddingRight: 8,
  },
  floatingLabels: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    position: 'absolute',
    bottom: -12,
    alignSelf: 'center',
    width: 225,
  },
  floatingLabel: {
    fontSize: 10,
    letterSpacing: 1,
    opacity: 1,
    textAlign: 'center',
    width: 75,
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },

  // Permission styles
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(248, 248, 252, 1)', // Match app background
  },
  permissionText: {
    textAlign: 'center',
    marginBottom: BrutalistTheme.spacing.md,
  },
  permissionDescription: {
    textAlign: 'center',
    marginBottom: BrutalistTheme.spacing.xl,
    paddingHorizontal: BrutalistTheme.spacing.lg,
  },
  permissionButton: {
    width: '80%',
  },

  // Loading styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(248, 248, 252, 1)', // Match app background
  },
  loadingText: {
    marginTop: BrutalistTheme.spacing.md,
  },
});
