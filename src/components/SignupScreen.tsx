import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TextInput,
  Alert,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { BrutalistText, BrutalistButton } from './index';
import { BrutalistTheme } from '../theme/colors';
import { useAuth } from '../contexts/AuthContext';

interface SignupScreenProps {
  onSwitchToLogin: () => void;
}

export const SignupScreen: React.FC<SignupScreenProps> = ({
  onSwitchToLogin,
}) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const {
    signUp,
    createProfile,
  } = useAuth();

  const validateForm = () => {
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter your email address');
      return false;
    }
    if (!fullName.trim()) {
      Alert.alert('Error', 'Please enter your display name');
      return false;
    }
    if (password.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters');
      return false;
    }
    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return false;
    }
    return true;
  };

  const handleSignup = async () => {
    if (!validateForm()) return;

    setIsLoading(true);

    // Sign up the user
    const { data, error } = await signUp(email.trim(), password);

    if (error) {
      Alert.alert('Signup Failed', error.message);
      setIsLoading(false);
      return;
    }

    // If signup successful
    if (data.user) {
      console.log(
        'Signup successful - User:',
        data.user.email,
        'Session:',
        !!data.session,
      );

      // Only create profile if we have a session (email verification disabled)
      // If email verification is enabled, profile will be created after email confirmation
      if (data.session) {
        console.log(
          'Creating profile immediately (email verification disabled)',
        );
        const { error: profileError } = await createProfile({
          full_name: fullName.trim(),
        });

        if (profileError) {
          console.warn('Profile creation failed:', profileError.message);
        }
      } else {
        console.log(
          'No session - email verification required, profile will be created after confirmation',
        );
      }

      // Only show email verification alert if no session (email verification enabled)
      if (!data.session) {
        Alert.alert(
          'Account Created!',
          'Please check your email to verify your account, then return to the app.',
          [{ text: 'OK', onPress: onSwitchToLogin }],
        );
      }
      // If data.session exists, user is automatically signed in - no alert needed
    }

    setIsLoading(false);
  };


  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={'rgba(248, 248, 252, 1)'}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={styles.keyboardAvoid}
      >
        <View style={styles.content}>
          {/* Top Section */}
          <View style={styles.topSection}>
            {/* Header - Clean like main app */}
            <View style={styles.header}>
              <BrutalistText
                variant="title1"
                fontFamily="system"
                weight="black"
                color="grey800"
              >
                KOA
              </BrutalistText>
              <BrutalistText
                variant="body"
                fontFamily="serif"
                weight="regular"
                color="grey700"
                style={styles.expandedSubtitle}
              >
                know our appetite
              </BrutalistText>
            </View>

            {/* Form Section Title */}
            <View style={styles.formTitleContainer}>
              <BrutalistText
                variant="caption1"
                fontFamily="system"
                weight="medium"
                color="grey600"
                style={styles.formTitle}
              >
                CREATE YOUR ACCOUNT
              </BrutalistText>
            </View>

            {/* Form - Clean inputs */}
            <View style={styles.form}>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={fullName}
                onChangeText={setFullName}
                placeholder="Display name"
                placeholderTextColor={BrutalistTheme.colors.grey500}
                autoCapitalize="words"
                returnKeyType="next"
              />
            </View>

            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={email}
                onChangeText={setEmail}
                placeholder="Email address"
                placeholderTextColor={BrutalistTheme.colors.grey500}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                returnKeyType="next"
              />
            </View>

            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={password}
                onChangeText={setPassword}
                placeholder="Password"
                placeholderTextColor={BrutalistTheme.colors.grey500}
                secureTextEntry
                returnKeyType="next"
              />
            </View>

            <View style={styles.inputContainer}>
              <TextInput
                style={styles.input}
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                placeholder="Confirm password"
                placeholderTextColor={BrutalistTheme.colors.grey500}
                secureTextEntry
                returnKeyType="go"
                onSubmitEditing={handleSignup}
              />
            </View>

            <TouchableOpacity
              style={[
                styles.signupButton,
                isLoading && styles.signupButtonDisabled,
              ]}
              onPress={handleSignup}
              disabled={isLoading}
              activeOpacity={0.7}
            >
              <BrutalistText
                variant="callout"
                weight="bold"
                color="white"
                fontFamily="system"
              >
                {isLoading ? 'Creating Account...' : 'Create Account'}
              </BrutalistText>
            </TouchableOpacity>
            </View>
          </View>

          {/* Footer - Enhanced Sign In CTA */}
          <View style={styles.footer}>
            <View style={styles.signInContainer}>
              <BrutalistText
                variant="callout"
                weight="regular"
                color="grey600"
                fontFamily="system"
                style={styles.signInPrompt}
              >
                Already have an account?
              </BrutalistText>
              
              <TouchableOpacity
                onPress={onSwitchToLogin}
                style={styles.signInButton}
                activeOpacity={0.7}
              >
                <BrutalistText
                  variant="callout"
                  weight="bold"
                  color="accent1"
                  fontFamily="system"
                >
                  Sign In
                </BrutalistText>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(248, 248, 252, 1)', // Match app background
  },
  keyboardAvoid: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingVertical: BrutalistTheme.spacing.lg,
    justifyContent: 'space-between',
  },

  // Top section with header and form
  topSection: {
    justifyContent: 'flex-start',
    paddingTop: BrutalistTheme.spacing.xl,
  },

  // Header - Clean
  header: {
    alignItems: 'center',
    marginBottom: BrutalistTheme.spacing.xxl,
    gap: BrutalistTheme.spacing.xs,
  },
  expandedSubtitle: {
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: BrutalistTheme.spacing.xs,
  },

  // Form section title
  formTitleContainer: {
    marginBottom: BrutalistTheme.spacing.md,
  },
  formTitle: {
    letterSpacing: 1,
    textAlign: 'left',
  },

  // Form - Clean
  form: {
    gap: BrutalistTheme.spacing.md,
  },
  inputContainer: {
    marginBottom: BrutalistTheme.spacing.sm,
  },
  input: {
    borderBottomWidth: 2,
    borderBottomColor: BrutalistTheme.colors.grey300,
    paddingHorizontal: 0,
    paddingVertical: BrutalistTheme.spacing.md,
    fontSize: BrutalistTheme.fontSizes.body,
    fontFamily: BrutalistTheme.fonts.system,
    backgroundColor: 'rgba(248, 248, 252, 1)', // Match app background
    color: BrutalistTheme.colors.grey800,
  },
  signupButton: {
    backgroundColor: BrutalistTheme.colors.black,
    paddingVertical: BrutalistTheme.spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: BrutalistTheme.spacing.lg,
    borderWidth: 1,
    borderColor: BrutalistTheme.colors.black,
    borderRadius: BrutalistTheme.borderRadius.sm,
  },
  signupButtonDisabled: {
    backgroundColor: BrutalistTheme.colors.grey400,
    borderColor: BrutalistTheme.colors.grey400,
  },


  // Footer - Enhanced Sign In CTA
  footer: {
    alignItems: 'center',
    paddingTop: BrutalistTheme.spacing.xl,
    paddingBottom: BrutalistTheme.spacing.md,
  },
  signInContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BrutalistTheme.colors.surface,
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.sm,
    borderRadius: BrutalistTheme.borderRadius.md,
    gap: BrutalistTheme.spacing.xs,
  },
  signInPrompt: {
    textAlign: 'center',
  },
  signInButton: {
    paddingVertical: BrutalistTheme.spacing.xs,
    paddingHorizontal: BrutalistTheme.spacing.xs,
  },
});
