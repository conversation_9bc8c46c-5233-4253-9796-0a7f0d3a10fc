import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  SafeAreaView,
  TouchableOpacity,
  Animated,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { BrutalistText, AnimatedConfidence, BrutalistView } from './index';
import { FoodSearchModal } from './FoodSearchModal';
import { BrutalistTheme } from '../theme/colors';
import { FoodItem, LoggedFood } from '../types/food';
import { useStaggeredAnimation, useCounterAnimation } from '../hooks';
import { useSyncStatus } from '../hooks/useSyncStatus';
import {
  getPortionDisplayText,
  getDynamicPortionDisplayText,
} from '../utils/quantityParser';
import { detectMealType, getMealDisplayName } from '../utils/mealTimeDetection';

interface FoodResultsScreenProps {
  foods: FoodItem[];
  confidence: number;
  dishName?: string;
  onLogFood: (food: LoggedFood) => Promise<void>;
  onLogMealSession?: (foods: LoggedFood[]) => Promise<void>;
  onRetakePicture: () => void;
  onBatchScan?: () => void;
  onGoHome?: () => void;
  autoOpenSearch?: boolean;
}

interface FoodSelection {
  selected: boolean;
  quantity: number;
}

interface EditedNutrition {
  calories?: number;
  protein?: number;
  carbs?: number;
  fat?: number;
  fiber?: number;
  sugar?: number;
  sodium?: number;
  cholesterol?: number;
}

export const FoodResultsScreen: React.FC<FoodResultsScreenProps> = ({
  foods,
  confidence,
  dishName,
  onLogFood,
  onLogMealSession,
  onRetakePicture,
  onBatchScan,
  onGoHome,
  autoOpenSearch = false,
}) => {
  // Initialize with all foods selected by default and auto-detected quantities
  const [foodSelections, setFoodSelections] = useState<
    Map<string, FoodSelection>
  >(() => {
    const initialSelections = new Map<string, FoodSelection>();
    foods.forEach(food => {
      // Use structured portion_count directly (defaults to 1 if not present)
      const detectedQuantity = food.portion_count || 1;
      initialSelections.set(food.id, {
        selected: true,
        quantity: detectedQuantity,
      });
    });
    return initialSelections;
  });
  const [mealType, setMealType] = useState<LoggedFood['meal_type']>(
    detectMealType(),
  );
  const [showMealTypeSelector, setShowMealTypeSelector] = useState(false);
  const [editingQuantity, setEditingQuantity] = useState<string | null>(null);
  const [editingNutrition, setEditingNutrition] = useState<string | null>(null);
  const [editedNutritionValues, setEditedNutritionValues] = useState<
    Map<string, EditedNutrition>
  >(new Map());
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [isLogging, setIsLogging] = useState(false);
  const [showFoodSearch, setShowFoodSearch] = useState(false);
  const [additionalFoods, setAdditionalFoods] = useState<FoodItem[]>([]);
  const mealTypeAnimation = useRef(new Animated.Value(0)).current;
  const { isPending, pendingCount } = useSyncStatus();

  // Combine scanned foods with additional searched foods
  const allFoods = [...foods, ...additionalFoods];

  // Animation hooks
  const cardAnimation = useStaggeredAnimation({
    itemCount: allFoods.length,
    staggerDelay: 150,
    enableHaptics: true,
  });

  // Start animations when component mounts or when item count changes
  useEffect(() => {
    // Ensure animated values are initialized before starting the animation
    if (cardAnimation.animatedValues.length === allFoods.length) {
      cardAnimation.startAnimation();
    }
  }, [allFoods.length, cardAnimation.animatedValues]);

  // Auto-open search modal if requested
  useEffect(() => {
    if (autoOpenSearch) {
      setShowFoodSearch(true);
    }
  }, [autoOpenSearch]);

  // Animate meal type selector
  useEffect(() => {
    Animated.spring(mealTypeAnimation, {
      toValue: showMealTypeSelector ? 1 : 0,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  }, [showMealTypeSelector]);

  const updateFoodQuantity = (foodId: string, quantity: number) => {
    setFoodSelections(prev => {
      const newSelections = new Map(prev);
      const current = newSelections.get(foodId) || {
        selected: true,
        quantity: 1,
      };

      // Calculate the new quantity, allowing it to go to 0
      const newQuantity = Math.max(0, quantity);

      // Auto-deselect when quantity reaches 0, otherwise keep selected
      const selected = newQuantity > 0;

      newSelections.set(foodId, { selected, quantity: newQuantity });
      return newSelections;
    });
  };

  const getSelectedFoods = () => {
    return allFoods.filter(
      food => foodSelections.get(food.id)?.selected === true,
    );
  };

  const getTotalCalories = () => {
    return getSelectedFoods().reduce((total, food) => {
      const selection = getFoodSelection(food.id);
      const editedNutrition = editedNutritionValues.get(food.id);
      const calories = editedNutrition?.calories ?? food.calories ?? 0;
      return total + calories * selection.quantity;
    }, 0);
  };

  const getFoodSelection = (foodId: string): FoodSelection => {
    return foodSelections.get(foodId) || { selected: false, quantity: 1 };
  };

  const formatNutritionValue = (value: number): string => {
    const result = value.toFixed(1);
    return result.endsWith('.0') ? result.slice(0, -2) : result;
  };

  const updateNutritionValue = (
    foodId: string,
    field: keyof EditedNutrition,
    value: number,
  ) => {
    setEditedNutritionValues(prev => {
      const newMap = new Map(prev);
      const current = newMap.get(foodId) || {};
      newMap.set(foodId, { ...current, [field]: value });
      return newMap;
    });
  };

  const getNutritionValue = (
    food: FoodItem,
    field: keyof EditedNutrition,
  ): number => {
    const edited = editedNutritionValues.get(food.id)?.[field];
    return edited !== undefined ? edited : food[field] || 0;
  };

  const isNutritionEdited = (
    foodId: string,
    field: keyof EditedNutrition,
  ): boolean => {
    return editedNutritionValues.get(foodId)?.[field] !== undefined;
  };

  const handleLogFood = async () => {
    const selectedFoods = getSelectedFoods();

    if (selectedFoods.length === 0) {
      Alert.alert('Error', 'Please select at least one food item');
      return;
    }

    if (isLogging) {
      return; // Prevent multiple submissions
    }

    setIsLogging(true);

    try {
      // Create LoggedFood objects for all selected foods
      const loggedFoods: LoggedFood[] = selectedFoods.map(food => {
        const selection = getFoodSelection(food.id);
        const editedNutrition = editedNutritionValues.get(food.id);
        return {
          ...food,
          // Apply edited nutrition values if they exist
          ...(editedNutrition && {
            calories: editedNutrition.calories ?? food.calories,
            protein: editedNutrition.protein ?? food.protein,
            carbs: editedNutrition.carbs ?? food.carbs,
            fat: editedNutrition.fat ?? food.fat,
            fiber: editedNutrition.fiber ?? food.fiber,
            sugar: editedNutrition.sugar ?? food.sugar,
            sodium: editedNutrition.sodium ?? food.sodium,
            cholesterol: editedNutrition.cholesterol ?? food.cholesterol,
          }),
          quantity: selection.quantity,
          meal_type: mealType,
          logged_at: new Date().toISOString(),
        };
      });

      // Use batch logging if available (multiple foods), otherwise individual logging
      if (onLogMealSession && loggedFoods.length > 1) {
        await onLogMealSession(loggedFoods);
      } else if (loggedFoods.length === 1) {
        await onLogFood(loggedFoods[0]);
      } else {
        // Fallback: log individually
        for (const food of loggedFoods) {
          await onLogFood(food);
        }
      }
    } catch (error) {
      console.error('Error logging food:', error);
      // The error handling is already done in the parent component
    } finally {
      setIsLogging(false);
    }
  };

  const handleAddFood = async (food: LoggedFood) => {
    if (isLogging) return;

    try {
      // Convert LoggedFood back to FoodItem and add to additional foods
      const foodItem: FoodItem = {
        id: food.id,
        name: food.name,
        calories: food.calories,
        protein: food.protein,
        carbs: food.carbs,
        fat: food.fat,
        serving_size: food.serving_size,
        confidence: food.confidence,
        fiber: food.fiber,
        sugar: food.sugar,
        sodium: food.sodium,
        cholesterol: food.cholesterol,
      };

      // Add to additional foods list
      setAdditionalFoods(prev => [...prev, foodItem]);

      // Add to food selections with the specified quantity
      setFoodSelections(prev => {
        const newSelections = new Map(prev);
        newSelections.set(food.id, {
          selected: true,
          quantity: food.quantity,
        });
        return newSelections;
      });

      // Close the search modal
      setShowFoodSearch(false);
    } catch (error) {
      console.error('Error adding food from search:', error);
      throw error; // Re-throw to let modal handle error display
    }
  };

  const toggleFoodSelection = (foodId: string) => {
    const selection = getFoodSelection(foodId);
    if (selection.selected) {
      // If selected, deselect by setting quantity to 0
      updateFoodQuantity(foodId, 0);
    } else {
      // If deselected, reselect by setting quantity to original portion_count or 1
      const food = allFoods.find(f => f.id === foodId);
      const originalQuantity = food?.portion_count || 1;
      updateFoodQuantity(foodId, originalQuantity);
    }
  };

  const toggleExpanded = (foodId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(foodId)) {
        console.log('Removing from expanded:', foodId);
        newSet.delete(foodId);
      } else {
        console.log('Adding to expanded:', foodId);
        newSet.add(foodId);
      }
      console.log('New expanded set:', Array.from(newSet));
      return newSet;
    });
  };

  const renderFoodItem = (food: FoodItem, index: number) => {
    const selection = getFoodSelection(food.id);
    const isSelected = selection.selected;
    const isExpanded = expandedItems.has(food.id);

    return (
      <Animated.View key={food.id} style={[cardAnimation.getItemStyle(index)]}>
        <View style={styles.foodRow}>
          <TouchableOpacity
            onPress={() => toggleFoodSelection(food.id)}
            style={styles.foodContent}
            activeOpacity={0.8}
          >
            <View style={styles.foodInfo}>
              <BrutalistText
                variant="body"
                weight="medium"
                color="black"
                style={styles.foodName}
              >
                {food.name}
              </BrutalistText>

              {/* Simple quantity */}
              {editingQuantity === food.id ? (
                <TextInput
                  style={styles.quantityInput}
                  value={selection.quantity.toString()}
                  onChangeText={text => {
                    const num = parseFloat(text) || 0;
                    updateFoodQuantity(food.id, num);
                  }}
                  onBlur={() => setEditingQuantity(null)}
                  onSubmitEditing={() => setEditingQuantity(null)}
                  keyboardType="numeric"
                  selectTextOnFocus
                  autoFocus
                  maxLength={4}
                />
              ) : (
                <TouchableOpacity
                  onPress={e => {
                    e.stopPropagation();
                    setEditingQuantity(food.id);
                  }}
                  style={styles.quantityButton}
                  activeOpacity={0.6}
                >
                  <BrutalistText
                    variant="caption1"
                    weight="medium"
                    fontFamily="mono"
                    color="grey600"
                  >
                    {selection.quantity}×
                  </BrutalistText>
                </TouchableOpacity>
              )}
            </View>

            {/* Selection underline */}
            {isSelected && <View style={styles.selectionUnderline} />}
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              console.log(
                'Expand clicked for:',
                food.name,
                'Current expanded:',
                isExpanded,
              );
              toggleExpanded(food.id);
            }}
            style={styles.expandButton}
            activeOpacity={0.6}
          >
            <BrutalistText variant="caption1" weight="medium" color="grey500">
              {isExpanded ? '−' : '+'}
            </BrutalistText>
          </TouchableOpacity>
        </View>

        {/* Expanded Content - Nutrition Details */}
        {isExpanded ? (
          <View style={styles.expandedContent}>
            {/* Header showing total nutrition for selected quantity */}
            <View style={styles.nutritionHeader}>
              <View style={styles.nutritionHeaderRow}>
                <BrutalistText
                  variant="caption1"
                  weight="medium"
                  color="grey600"
                  fontFamily="mono"
                >
                  TOTAL NUTRITION ({selection.quantity}×)
                </BrutalistText>

                {/* Portion tooltip - small info indicator */}
                {getDynamicPortionDisplayText(food, selection.quantity) && (
                  <TouchableOpacity
                    onPress={e => {
                      e.stopPropagation();
                      // Toggle portion tooltip for this specific food
                      const currentExpanded = expandedItems.has(
                        `${food.id}-portion`,
                      );
                      setExpandedItems(prev => {
                        const newSet = new Set(prev);
                        if (currentExpanded) {
                          newSet.delete(`${food.id}-portion`);
                        } else {
                          newSet.add(`${food.id}-portion`);
                        }
                        return newSet;
                      });
                    }}
                    style={styles.portionTooltipButton}
                    activeOpacity={0.6}
                  >
                    <BrutalistText
                      variant="caption2"
                      weight="bold"
                      color="grey500"
                      fontFamily="mono"
                    >
                      ⓘ
                    </BrutalistText>
                  </TouchableOpacity>
                )}
              </View>

              {/* Portion tooltip content - appears below title when active */}
              {getDynamicPortionDisplayText(food, selection.quantity) &&
                expandedItems.has(`${food.id}-portion`) && (
                  <View style={styles.portionTooltip}>
                    <BrutalistText
                      variant="caption1"
                      weight="medium"
                      color="grey600"
                      fontFamily="serif"
                      style={styles.portionTooltipText}
                    >
                      {getDynamicPortionDisplayText(food, selection.quantity)}
                    </BrutalistText>
                  </View>
                )}
            </View>

            {/* Primary Nutrition Row - multiply by quantity */}
            <View style={styles.nutritionGrid}>
              <View style={styles.macroItem}>
                {editingNutrition === `${food.id}-calories` ? (
                  <TextInput
                    style={styles.nutritionInput}
                    value={getNutritionValue(food, 'calories').toString()}
                    onChangeText={text => {
                      const num = parseFloat(text) || 0;
                      updateNutritionValue(food.id, 'calories', num);
                    }}
                    onBlur={() => setEditingNutrition(null)}
                    onSubmitEditing={() => setEditingNutrition(null)}
                    keyboardType="numeric"
                    selectTextOnFocus
                    autoFocus
                    maxLength={5}
                  />
                ) : (
                  <TouchableOpacity
                    onPress={e => {
                      e.stopPropagation();
                      setEditingNutrition(`${food.id}-calories`);
                    }}
                    style={styles.nutritionButton}
                    activeOpacity={0.6}
                  >
                    <BrutalistText
                      variant="caption1"
                      weight="semibold"
                      fontFamily="mono"
                      color="black"
                    >
                      {Math.round(
                        getNutritionValue(food, 'calories') *
                          selection.quantity,
                      )}
                    </BrutalistText>
                    {isNutritionEdited(food.id, 'calories') && (
                      <View style={styles.editedIndicator} />
                    )}
                  </TouchableOpacity>
                )}
                <BrutalistText
                  variant="caption2"
                  weight="medium"
                  color="grey600"
                >
                  CALORIES
                </BrutalistText>
              </View>

              <View style={styles.macroItem}>
                {editingNutrition === `${food.id}-protein` ? (
                  <TextInput
                    style={styles.nutritionInput}
                    value={getNutritionValue(food, 'protein').toString()}
                    onChangeText={text => {
                      const num = parseFloat(text) || 0;
                      updateNutritionValue(food.id, 'protein', num);
                    }}
                    onBlur={() => setEditingNutrition(null)}
                    onSubmitEditing={() => setEditingNutrition(null)}
                    keyboardType="numeric"
                    selectTextOnFocus
                    autoFocus
                    maxLength={5}
                  />
                ) : (
                  <TouchableOpacity
                    onPress={e => {
                      e.stopPropagation();
                      setEditingNutrition(`${food.id}-protein`);
                    }}
                    style={styles.nutritionButton}
                    activeOpacity={0.6}
                  >
                    <BrutalistText
                      variant="caption1"
                      weight="semibold"
                      fontFamily="mono"
                      color="black"
                    >
                      {formatNutritionValue(
                        getNutritionValue(food, 'protein') * selection.quantity,
                      )}
                      g
                    </BrutalistText>
                    {isNutritionEdited(food.id, 'protein') && (
                      <View style={styles.editedIndicator} />
                    )}
                  </TouchableOpacity>
                )}
                <BrutalistText
                  variant="caption2"
                  weight="medium"
                  color="grey600"
                >
                  PROTEIN
                </BrutalistText>
              </View>

              <View style={styles.macroItem}>
                {editingNutrition === `${food.id}-carbs` ? (
                  <TextInput
                    style={styles.nutritionInput}
                    value={getNutritionValue(food, 'carbs').toString()}
                    onChangeText={text => {
                      const num = parseFloat(text) || 0;
                      updateNutritionValue(food.id, 'carbs', num);
                    }}
                    onBlur={() => setEditingNutrition(null)}
                    onSubmitEditing={() => setEditingNutrition(null)}
                    keyboardType="numeric"
                    selectTextOnFocus
                    autoFocus
                    maxLength={5}
                  />
                ) : (
                  <TouchableOpacity
                    onPress={e => {
                      e.stopPropagation();
                      setEditingNutrition(`${food.id}-carbs`);
                    }}
                    style={styles.nutritionButton}
                    activeOpacity={0.6}
                  >
                    <BrutalistText
                      variant="caption1"
                      weight="semibold"
                      fontFamily="mono"
                      color="black"
                    >
                      {formatNutritionValue(
                        getNutritionValue(food, 'carbs') * selection.quantity,
                      )}
                      g
                    </BrutalistText>
                    {isNutritionEdited(food.id, 'carbs') && (
                      <View style={styles.editedIndicator} />
                    )}
                  </TouchableOpacity>
                )}
                <BrutalistText
                  variant="caption2"
                  weight="medium"
                  color="grey600"
                >
                  CARBS
                </BrutalistText>
              </View>

              <View style={styles.macroItem}>
                {editingNutrition === `${food.id}-fat` ? (
                  <TextInput
                    style={styles.nutritionInput}
                    value={getNutritionValue(food, 'fat').toString()}
                    onChangeText={text => {
                      const num = parseFloat(text) || 0;
                      updateNutritionValue(food.id, 'fat', num);
                    }}
                    onBlur={() => setEditingNutrition(null)}
                    onSubmitEditing={() => setEditingNutrition(null)}
                    keyboardType="numeric"
                    selectTextOnFocus
                    autoFocus
                    maxLength={5}
                  />
                ) : (
                  <TouchableOpacity
                    onPress={e => {
                      e.stopPropagation();
                      setEditingNutrition(`${food.id}-fat`);
                    }}
                    style={styles.nutritionButton}
                    activeOpacity={0.6}
                  >
                    <BrutalistText
                      variant="caption1"
                      weight="semibold"
                      fontFamily="mono"
                      color="black"
                    >
                      {formatNutritionValue(
                        getNutritionValue(food, 'fat') * selection.quantity,
                      )}
                      g
                    </BrutalistText>
                    {isNutritionEdited(food.id, 'fat') && (
                      <View style={styles.editedIndicator} />
                    )}
                  </TouchableOpacity>
                )}
                <BrutalistText
                  variant="caption2"
                  weight="medium"
                  color="grey600"
                >
                  FAT
                </BrutalistText>
              </View>
            </View>

            {/* Secondary Nutrition Row - multiply by quantity */}
            {(food.fiber || food.sugar || food.sodium || food.cholesterol) && (
              <View style={styles.nutritionGrid}>
                {(food.fiber !== undefined ||
                  isNutritionEdited(food.id, 'fiber')) && (
                  <View style={styles.macroItem}>
                    {editingNutrition === `${food.id}-fiber` ? (
                      <TextInput
                        style={styles.nutritionInput}
                        value={getNutritionValue(food, 'fiber').toString()}
                        onChangeText={text => {
                          const num = parseFloat(text) || 0;
                          updateNutritionValue(food.id, 'fiber', num);
                        }}
                        onBlur={() => setEditingNutrition(null)}
                        onSubmitEditing={() => setEditingNutrition(null)}
                        keyboardType="numeric"
                        selectTextOnFocus
                        autoFocus
                        maxLength={5}
                      />
                    ) : (
                      <TouchableOpacity
                        onPress={e => {
                          e.stopPropagation();
                          setEditingNutrition(`${food.id}-fiber`);
                        }}
                        style={styles.nutritionButton}
                        activeOpacity={0.6}
                      >
                        <BrutalistText
                          variant="caption1"
                          weight="semibold"
                          fontFamily="mono"
                          color="black"
                        >
                          {formatNutritionValue(
                            getNutritionValue(food, 'fiber') *
                              selection.quantity,
                          )}
                          g
                        </BrutalistText>
                        {isNutritionEdited(food.id, 'fiber') && (
                          <View style={styles.editedIndicator} />
                        )}
                      </TouchableOpacity>
                    )}
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                    >
                      FIBER
                    </BrutalistText>
                  </View>
                )}

                {(food.sugar !== undefined ||
                  isNutritionEdited(food.id, 'sugar')) && (
                  <View style={styles.macroItem}>
                    {editingNutrition === `${food.id}-sugar` ? (
                      <TextInput
                        style={styles.nutritionInput}
                        value={getNutritionValue(food, 'sugar').toString()}
                        onChangeText={text => {
                          const num = parseFloat(text) || 0;
                          updateNutritionValue(food.id, 'sugar', num);
                        }}
                        onBlur={() => setEditingNutrition(null)}
                        onSubmitEditing={() => setEditingNutrition(null)}
                        keyboardType="numeric"
                        selectTextOnFocus
                        autoFocus
                        maxLength={5}
                      />
                    ) : (
                      <TouchableOpacity
                        onPress={e => {
                          e.stopPropagation();
                          setEditingNutrition(`${food.id}-sugar`);
                        }}
                        style={styles.nutritionButton}
                        activeOpacity={0.6}
                      >
                        <BrutalistText
                          variant="caption1"
                          weight="semibold"
                          fontFamily="mono"
                          color="black"
                        >
                          {formatNutritionValue(
                            getNutritionValue(food, 'sugar') *
                              selection.quantity,
                          )}
                          g
                        </BrutalistText>
                        {isNutritionEdited(food.id, 'sugar') && (
                          <View style={styles.editedIndicator} />
                        )}
                      </TouchableOpacity>
                    )}
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                    >
                      SUGAR
                    </BrutalistText>
                  </View>
                )}

                {(food.sodium !== undefined ||
                  isNutritionEdited(food.id, 'sodium')) && (
                  <View style={styles.macroItem}>
                    {editingNutrition === `${food.id}-sodium` ? (
                      <TextInput
                        style={styles.nutritionInput}
                        value={getNutritionValue(food, 'sodium').toString()}
                        onChangeText={text => {
                          const num = parseFloat(text) || 0;
                          updateNutritionValue(food.id, 'sodium', num);
                        }}
                        onBlur={() => setEditingNutrition(null)}
                        onSubmitEditing={() => setEditingNutrition(null)}
                        keyboardType="numeric"
                        selectTextOnFocus
                        autoFocus
                        maxLength={5}
                      />
                    ) : (
                      <TouchableOpacity
                        onPress={e => {
                          e.stopPropagation();
                          setEditingNutrition(`${food.id}-sodium`);
                        }}
                        style={styles.nutritionButton}
                        activeOpacity={0.6}
                      >
                        <BrutalistText
                          variant="caption1"
                          weight="semibold"
                          fontFamily="mono"
                          color="black"
                        >
                          {Math.round(
                            getNutritionValue(food, 'sodium') *
                              selection.quantity,
                          )}
                          mg
                        </BrutalistText>
                        {isNutritionEdited(food.id, 'sodium') && (
                          <View style={styles.editedIndicator} />
                        )}
                      </TouchableOpacity>
                    )}
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                    >
                      SODIUM
                    </BrutalistText>
                  </View>
                )}

                {(food.cholesterol !== undefined ||
                  isNutritionEdited(food.id, 'cholesterol')) && (
                  <View style={styles.macroItem}>
                    {editingNutrition === `${food.id}-cholesterol` ? (
                      <TextInput
                        style={styles.nutritionInput}
                        value={getNutritionValue(
                          food,
                          'cholesterol',
                        ).toString()}
                        onChangeText={text => {
                          const num = parseFloat(text) || 0;
                          updateNutritionValue(food.id, 'cholesterol', num);
                        }}
                        onBlur={() => setEditingNutrition(null)}
                        onSubmitEditing={() => setEditingNutrition(null)}
                        keyboardType="numeric"
                        selectTextOnFocus
                        autoFocus
                        maxLength={5}
                      />
                    ) : (
                      <TouchableOpacity
                        onPress={e => {
                          e.stopPropagation();
                          setEditingNutrition(`${food.id}-cholesterol`);
                        }}
                        style={styles.nutritionButton}
                        activeOpacity={0.6}
                      >
                        <BrutalistText
                          variant="caption1"
                          weight="semibold"
                          fontFamily="mono"
                          color="black"
                        >
                          {Math.round(
                            getNutritionValue(food, 'cholesterol') *
                              selection.quantity,
                          )}
                          mg
                        </BrutalistText>
                        {isNutritionEdited(food.id, 'cholesterol') && (
                          <View style={styles.editedIndicator} />
                        )}
                      </TouchableOpacity>
                    )}
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                    >
                      CHOL
                    </BrutalistText>
                  </View>
                )}
              </View>
            )}
          </View>
        ) : null}
      </Animated.View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Clean Header - No Back Button */}
      <View style={styles.header} />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          {/* Compact Summary */}
          <BrutalistView
            variant="floating-attached"
            padding="md"
            style={{ marginBottom: BrutalistTheme.spacing.sm }}
          >
            {/* Dish Name */}
            {dishName && (
              <BrutalistText
                variant="caption1"
                weight="medium"
                color="grey600"
                style={styles.dishName}
              >
                {dishName}
              </BrutalistText>
            )}

            <View style={styles.detectedItems}>
              {foods.map((food, index) => (
                <React.Fragment key={food.id}>
                  <BrutalistText
                    variant="callout"
                    weight="medium"
                    color="black"
                  >
                    {food.name}
                  </BrutalistText>
                  <BrutalistText
                    variant="callout"
                    weight="medium"
                    color="success"
                    fontFamily="mono"
                    style={styles.confidenceText}
                  >
                    (
                    <AnimatedConfidence
                      confidence={food.confidence}
                      delay={index * 100 + 400}
                      variant="callout"
                      weight="medium"
                      color="success"
                      fontFamily="mono"
                    />
                    )
                  </BrutalistText>
                  {index < foods.length - 1 && (
                    <BrutalistText
                      variant="callout"
                      weight="medium"
                      color="grey500"
                    >
                      {' • '}
                    </BrutalistText>
                  )}
                </React.Fragment>
              ))}
            </View>

            {/* Compact Meal Type */}
            {!showMealTypeSelector ? (
              <TouchableOpacity
                onPress={() => setShowMealTypeSelector(true)}
                style={styles.mealTypeToggle}
                activeOpacity={0.7}
              >
                <BrutalistText
                  variant="caption1"
                  weight="medium"
                  color="grey600"
                  fontFamily="mono"
                >
                  {getMealDisplayName(mealType)} ⋅⋅⋅
                </BrutalistText>
              </TouchableOpacity>
            ) : (
              <Animated.View
                style={[
                  styles.mealTypeOptionsRow,
                  {
                    opacity: mealTypeAnimation,
                    transform: [
                      {
                        scaleX: mealTypeAnimation.interpolate({
                          inputRange: [0, 1],
                          outputRange: [0.3, 1],
                        }),
                      },
                    ],
                  },
                ]}
              >
                {[
                  { key: 'breakfast', label: 'BREAK' },
                  { key: 'lunch', label: 'LUNCH' },
                  { key: 'dinner', label: 'DINNER' },
                  { key: 'snack', label: 'SNACK' },
                ].map(meal => (
                  <TouchableOpacity
                    key={meal.key}
                    onPress={() => {
                      setMealType(meal.key as LoggedFood['meal_type']);
                      setTimeout(() => setShowMealTypeSelector(false), 100);
                    }}
                    style={styles.mealTypeOptionButton}
                    activeOpacity={0.7}
                  >
                    <BrutalistText
                      variant="caption1"
                      weight="bold"
                      fontFamily="mono"
                      color="grey700"
                    >
                      {meal.label}
                    </BrutalistText>
                  </TouchableOpacity>
                ))}
              </Animated.View>
            )}
          </BrutalistView>

          <View style={styles.foodsContainer}>
            {allFoods.map((food, index) => renderFoodItem(food, index))}
          </View>
        </View>
      </ScrollView>

      <View style={styles.actions}>
        {/* Secondary Actions */}
        <View style={styles.secondaryActionsRow}>
          {onBatchScan && (
            <TouchableOpacity
              onPress={onBatchScan}
              style={styles.secondaryActionButton}
              activeOpacity={0.6}
              disabled={isLogging}
            >
              <BrutalistText
                variant="body"
                weight="medium"
                color={isLogging ? 'grey500' : 'grey700'}
                fontFamily="system"
                style={styles.secondaryActionText}
              >
                + Scan
              </BrutalistText>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            onPress={() => setShowFoodSearch(true)}
            style={styles.secondaryActionButton}
            activeOpacity={0.6}
            disabled={isLogging}
          >
            <BrutalistText
              variant="body"
              weight="medium"
              color={isLogging ? 'grey500' : 'grey700'}
              fontFamily="system"
              style={styles.secondaryActionText}
            >
              + Search
            </BrutalistText>
          </TouchableOpacity>
        </View>

        {/* Primary Action Buttons */}
        <View style={styles.primaryActionButtons}>
          <TouchableOpacity
            onPress={onRetakePicture}
            style={styles.retakeAction}
            activeOpacity={0.7}
          >
            <BrutalistText
              variant="body"
              weight="medium"
              color="grey700"
              fontFamily="system"
            >
              ← Retake
            </BrutalistText>
          </TouchableOpacity>

          {/* Selection Status */}
          <View style={styles.selectionStatus}>
            <View style={styles.selectionStatusRow}>
              <BrutalistText
                variant="caption1"
                weight="medium"
                color="grey600"
                fontFamily="mono"
              >
                {getSelectedFoods().length}/{allFoods.length}
              </BrutalistText>
              {getSelectedFoods().length > 0 && (
                <BrutalistText
                  variant="caption1"
                  weight="bold"
                  color="accent1"
                  fontFamily="mono"
                >
                  {' '}
                  + {Math.round(getTotalCalories())} CALS
                </BrutalistText>
              )}
            </View>
          </View>

          <TouchableOpacity
            onPress={handleLogFood}
            style={[
              styles.logAction,
              (getSelectedFoods().length === 0 || isLogging) &&
                styles.disabledAction,
            ]}
            activeOpacity={0.7}
            disabled={getSelectedFoods().length === 0 || isLogging}
          >
            <View style={styles.logButtonContent}>
              {isLogging && (
                <ActivityIndicator
                  size="small"
                  color={BrutalistTheme.colors.grey600}
                  style={styles.loadingIndicator}
                />
              )}
              <BrutalistText
                variant="body"
                weight="medium"
                color={
                  getSelectedFoods().length === 0 || isLogging
                    ? 'grey500'
                    : 'black'
                }
                fontFamily="system"
              >
                {isLogging ? 'Saving...' : 'Log Food'}
              </BrutalistText>
            </View>
            <View
              style={[
                styles.logActionUnderline,
                (getSelectedFoods().length === 0 || isLogging) &&
                  styles.disabledUnderline,
              ]}
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Food Search Modal */}
      <FoodSearchModal
        visible={showFoodSearch}
        mealType={mealType}
        onAddFood={handleAddFood}
        onCancel={() => setShowFoodSearch(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(248, 248, 252, 1)', // Match app background
  },
  scrollView: {
    flex: 1,
  },

  // Minimal Header Styles
  header: {
    backgroundColor: 'rgba(248, 248, 252, 1)', // Match app background
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingTop: BrutalistTheme.spacing.xs,
    paddingBottom: BrutalistTheme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(88, 86, 214, 0.06)', // Subtle purple tinted divider
    alignItems: 'center',
  },

  // Content Layout
  content: {
    flex: 1,
    paddingTop: 0, // Remove top padding to connect with header
    paddingHorizontal: 0, // Remove horizontal padding for summary section
    paddingBottom: 0, // Remove bottom padding
    gap: BrutalistTheme.spacing.xs, // Reduce gap between sections
  },
  foodsContainer: {
    gap: 0,
  },
  addFoodContainer: {
    alignItems: 'center',
    paddingTop: BrutalistTheme.spacing.md,
    paddingBottom: BrutalistTheme.spacing.sm,
  },
  sectionTitle: {
    marginBottom: BrutalistTheme.spacing.md,
    letterSpacing: 1,
  },

  // Clean Food Row
  foodRow: {
    backgroundColor: 'rgba(255, 255, 255, 0.96)',
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 44,
    marginHorizontal: BrutalistTheme.spacing.lg,
    marginBottom: BrutalistTheme.spacing.xs,
    borderRadius: BrutalistTheme.borderRadius.md,
    shadowColor: '#5856D6',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.02,
    shadowRadius: 4,
    elevation: 1,
  },

  foodContent: {
    flex: 1,
    paddingVertical: BrutalistTheme.spacing.sm,
    paddingHorizontal: BrutalistTheme.spacing.md,
  },

  foodInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  foodName: {
    flex: 1,
    marginRight: BrutalistTheme.spacing.sm,
  },

  // Portion Tooltip
  portionTooltipButton: {
    width: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: BrutalistTheme.spacing.xs,
  },

  portionTooltip: {
    marginTop: BrutalistTheme.spacing.xs,
    paddingVertical: BrutalistTheme.spacing.xs,
    paddingHorizontal: BrutalistTheme.spacing.sm,
    backgroundColor: BrutalistTheme.colors.surface,
    borderRadius: BrutalistTheme.borderRadius.xs,
    borderWidth: BrutalistTheme.borderWidth.hairline,
    borderColor: BrutalistTheme.colors.grey300,
  },

  portionTooltipText: {
    lineHeight: 16,
    fontStyle: 'italic',
  },

  selectionUnderline: {
    height: 3,
    backgroundColor: BrutalistTheme.colors.grey600,
    marginTop: 2,
    borderRadius: 0,
  },

  quantityButton: {
    paddingHorizontal: BrutalistTheme.spacing.xs,
    paddingVertical: 2,
    minWidth: 32,
    alignItems: 'center',
  },

  quantityInput: {
    fontSize: 14,
    fontWeight: 'medium',
    fontFamily: BrutalistTheme.fonts.mono,
    textAlign: 'center',
    minWidth: 32,
    paddingHorizontal: BrutalistTheme.spacing.xs,
    paddingVertical: 2,
    borderWidth: 0,
    backgroundColor: 'transparent',
    color: BrutalistTheme.colors.grey600,
  },

  nutritionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: BrutalistTheme.spacing.xs,
    paddingVertical: 2,
    position: 'relative',
  },

  nutritionInput: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: BrutalistTheme.fonts.mono,
    textAlign: 'center',
    minWidth: 40,
    paddingHorizontal: BrutalistTheme.spacing.xs,
    paddingVertical: 2,
    borderWidth: 0,
    backgroundColor: 'transparent',
    color: BrutalistTheme.colors.black,
  },

  editedIndicator: {
    position: 'absolute',
    bottom: -1,
    left: '50%',
    transform: [{ translateX: -6 }],
    width: 12,
    height: 1,
    backgroundColor: BrutalistTheme.colors.success,
  },

  expandButton: {
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 40,
  },

  // Expanded Content
  expandedContent: {
    paddingTop: BrutalistTheme.spacing.sm,
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingBottom: BrutalistTheme.spacing.md,
    backgroundColor: 'rgba(255, 255, 255, 0.96)',
    marginHorizontal: BrutalistTheme.spacing.lg,
    borderBottomLeftRadius: BrutalistTheme.borderRadius.md,
    borderBottomRightRadius: BrutalistTheme.borderRadius.md,
    marginTop: -BrutalistTheme.spacing.xs,
  },

  nutritionHeader: {
    alignItems: 'center',
    marginBottom: BrutalistTheme.spacing.sm,
  },

  nutritionHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: BrutalistTheme.spacing.xs,
  },

  // Compact Summary Section - now handled by BrutalistView
  summarySection: {
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xs,
  },
  dishName: {
    marginBottom: BrutalistTheme.spacing.xs,
    textAlign: 'center',
  },
  detectedItems: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
  },
  confidenceText: {
    marginLeft: 2,
  },

  // Compact Meal Type Toggle
  mealTypeToggle: {
    paddingVertical: BrutalistTheme.spacing.xs,
    paddingHorizontal: BrutalistTheme.spacing.sm,
    alignItems: 'center',
  },

  // Meal Type Options Row - Replace the toggle in same position
  mealTypeOptionsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: BrutalistTheme.spacing.sm,
    paddingVertical: BrutalistTheme.spacing.sm,
  },
  mealTypeOptionButton: {
    paddingVertical: BrutalistTheme.spacing.xs,
    paddingHorizontal: BrutalistTheme.spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 28,
  },

  // Clean Nutrition Grid Layout
  nutritionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: BrutalistTheme.spacing.sm,
  },
  macroItem: {
    alignItems: 'center',
    flex: 1,
    gap: 2,
  },

  // Food Meta
  foodMeta: {
    gap: BrutalistTheme.spacing.xs,
  },
  foodMetaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  // Quantity Controls
  quantityRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: BrutalistTheme.spacing.xs,
  },

  // Quantity Selector & Input
  quantitySelector: {
    paddingVertical: BrutalistTheme.spacing.xs,
    paddingHorizontal: BrutalistTheme.spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 50,
  },

  // Clean Footer
  actions: {
    backgroundColor: 'rgba(248, 248, 252, 1)', // Match app background
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingTop: 0,
    paddingBottom: 0, // Remove bottom padding
    borderTopWidth: 1,
    borderTopColor: 'rgba(88, 86, 214, 0.06)', // Subtle purple tinted divider
    shadowColor: '#5856D6',
    shadowOffset: { width: 0, height: -1 },
    shadowOpacity: 0.02,
    shadowRadius: 4,
    elevation: 1,
  },
  selectionStatus: {
    alignItems: 'center',
    flex: 1,
  },
  selectionStatusRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  secondaryActionsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: BrutalistTheme.spacing.sm,
    gap: BrutalistTheme.spacing.xl,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(88, 86, 214, 0.06)', // Subtle purple tinted divider
  },
  secondaryActionButton: {
    paddingVertical: BrutalistTheme.spacing.sm,
    paddingHorizontal: BrutalistTheme.spacing.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: BrutalistTheme.borderRadius.sm,
    shadowColor: '#5856D6',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.02,
    shadowRadius: 2,
    elevation: 1,
  },
  secondaryActionText: {
    letterSpacing: 0.8,
    fontSize: 14,
  },
  primaryActionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: BrutalistTheme.spacing.sm,
    paddingBottom: 0, // Remove bottom padding
  },
  retakeAction: {
    paddingVertical: 0,
    paddingHorizontal: BrutalistTheme.spacing.xs,
  },
  logAction: {
    alignItems: 'flex-end',
    paddingVertical: 0,
  },
  logButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xs,
  },
  loadingIndicator: {
    marginRight: 2,
  },
  logActionUnderline: {
    height: 2,
    backgroundColor: BrutalistTheme.colors.grey700,
    marginTop: 2,
    width: '100%',
  },
  disabledAction: {
    opacity: 0.5,
  },
  disabledUnderline: {
    backgroundColor: BrutalistTheme.colors.grey400,
  },
});
