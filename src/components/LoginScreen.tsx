import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TextInput,
  Alert,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { BrutalistText, BrutalistButton } from './index';
import { BrutalistTheme } from '../theme/colors';
import { useAuth } from '../contexts/AuthContext';
import { localStorageService } from '../services/localStorageService';
import { AppleSignInButton } from './AppleSignInButton';
// import { GoogleSignInButton } from './GoogleSignInButton';
import { DiscordSignInButton } from './DiscordSignInButton';

interface LoginScreenProps {
  onSwitchToSignup: () => void;
  onSwitchToSms?: () => void;
  onSwitchToForgotPassword: () => void;
}

export const LoginScreen: React.FC<LoginScreenProps> = ({
  onSwitchToSignup,
  onSwitchToForgotPassword,
}) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isAppleLoading, setIsAppleLoading] = useState(false);
  // const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [isDiscordLoading, setIsDiscordLoading] = useState(false);
  const { signIn, signInWithApple, signInWithDiscord, isAppleSignInAvailable } =
    useAuth();

  // Load remembered email on mount
  useEffect(() => {
    const rememberedEmail = localStorageService.getRememberedEmail();
    if (rememberedEmail) {
      setEmail(rememberedEmail);
    }
  }, []);

  const handleLogin = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }

    setIsLoading(true);
    
    // Always save email for next time
    localStorageService.setRememberedEmail(email.trim());

    const { error } = await signIn(email.trim(), password);

    if (error) {
      Alert.alert('Login Failed', error.message);
    }

    setIsLoading(false);
  };

  const handleAppleSignIn = async () => {
    setIsAppleLoading(true);
    const { error } = await signInWithApple();

    if (error) {
      Alert.alert('Apple Sign-In Failed', error.message);
    }

    setIsAppleLoading(false);
  };

  // const handleGoogleSignIn = async () => {
  //   setIsGoogleLoading(true);
  //   const { error } = await signInWithGoogle();

  //   if (error) {
  //     Alert.alert('Google Sign-In Failed', error.message);
  //   }

  //   setIsGoogleLoading(false);
  // };

  const handleDiscordSignIn = async () => {
    setIsDiscordLoading(true);
    const { error } = await signInWithDiscord();

    if (error) {
      Alert.alert('Discord Sign-In Failed', error.message);
    }

    setIsDiscordLoading(false);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={BrutalistTheme.colors.white}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={styles.keyboardAvoid}
      >
        <View style={styles.content}>
          {/* Top Section */}
          <View style={styles.topSection}>
            {/* Header - Clean like main app */}
            <View style={styles.header}>
              <BrutalistText
                variant="title1"
                fontFamily="system"
                weight="black"
                color="grey800"
              >
                KOA
              </BrutalistText>
              <BrutalistText
                variant="body"
                fontFamily="serif"
                weight="regular"
                color="grey700"
                style={styles.expandedSubtitle}
              >
                know our appetite
              </BrutalistText>
            </View>

            {/* Form Section Title */}
            <View style={styles.formTitleContainer}>
              <BrutalistText
                variant="caption1"
                fontFamily="system"
                weight="medium"
                color="grey600"
                style={styles.formTitle}
              >
                SIGN IN TO YOUR ACCOUNT
              </BrutalistText>
            </View>

            {/* Form - Clean inputs */}
            <View style={styles.form}>
              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.input}
                  value={email}
                  onChangeText={setEmail}
                  placeholder="Email address"
                  placeholderTextColor={BrutalistTheme.colors.grey500}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  returnKeyType="next"
                />
              </View>

              <View style={styles.inputContainer}>
                <TextInput
                  style={styles.input}
                  value={password}
                  onChangeText={setPassword}
                  placeholder="Password"
                  placeholderTextColor={BrutalistTheme.colors.grey500}
                  secureTextEntry
                  returnKeyType="go"
                  onSubmitEditing={handleLogin}
                />
              </View>

              {/* Forgot Password Link - Near password field */}
              <TouchableOpacity
                onPress={onSwitchToForgotPassword}
                style={styles.forgotPasswordLink}
                activeOpacity={0.7}
              >
                <BrutalistText
                  variant="footnote"
                  weight="medium"
                  color="accent1"
                  fontFamily="system"
                >
                  Forgot your password?
                </BrutalistText>
              </TouchableOpacity>

              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  style={[
                    styles.primaryButton,
                    (isLoading || isAppleLoading || isDiscordLoading) &&
                      styles.primaryButtonDisabled,
                  ]}
                  onPress={handleLogin}
                  disabled={isLoading || isAppleLoading || isDiscordLoading}
                  activeOpacity={0.8}
                >
                  <BrutalistText
                    variant="callout"
                    weight="semibold"
                    color="white"
                    fontFamily="system"
                  >
                    {isLoading ? 'Signing In...' : 'Sign In'}
                  </BrutalistText>
                </TouchableOpacity>

                <View style={styles.dividerContainer}>
                  <View style={styles.dividerLine} />
                  <BrutalistText
                    variant="caption2"
                    color="grey500"
                    fontFamily="system"
                    style={styles.dividerText}
                  >
                    OR
                  </BrutalistText>
                  <View style={styles.dividerLine} />
                </View>

                {/* Social Sign-In Buttons */}
                <View style={styles.socialButtons}>
                  {isAppleSignInAvailable && (
                    <AppleSignInButton
                      onPress={handleAppleSignIn}
                      disabled={isLoading || isAppleLoading || isDiscordLoading}
                      loading={isAppleLoading}
                    />
                  )}

                  <DiscordSignInButton
                    onPress={handleDiscordSignIn}
                    disabled={isLoading || isAppleLoading || isDiscordLoading}
                    loading={isDiscordLoading}
                  />
                </View>
              </View>
            </View>
          </View>

          {/* Footer - Enhanced Create Account CTA */}
          <View style={styles.footer}>
            <View style={styles.createAccountContainer}>
              <BrutalistText
                variant="callout"
                weight="regular"
                color="grey600"
                fontFamily="system"
                style={styles.createAccountPrompt}
              >
                Don't have an account?
              </BrutalistText>

              <TouchableOpacity
                onPress={onSwitchToSignup}
                style={styles.createAccountButton}
                activeOpacity={0.7}
              >
                <BrutalistText
                  variant="callout"
                  weight="bold"
                  color="accent1"
                  fontFamily="system"
                >
                  Create Account
                </BrutalistText>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(248, 248, 252, 1)', // Match app background
  },
  keyboardAvoid: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingVertical: BrutalistTheme.spacing.lg,
    justifyContent: 'space-between',
  },

  // Top section with header and form
  topSection: {
    justifyContent: 'flex-start',
    paddingTop: BrutalistTheme.spacing.xl,
  },

  // Header - Clean
  header: {
    alignItems: 'center',
    marginBottom: BrutalistTheme.spacing.xxl,
    gap: BrutalistTheme.spacing.xs,
  },
  expandedSubtitle: {
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: BrutalistTheme.spacing.xs,
  },

  // Form section title
  formTitleContainer: {
    marginBottom: BrutalistTheme.spacing.md,
  },
  formTitle: {
    letterSpacing: 1,
    textAlign: 'left',
  },

  // Form - Clean
  form: {
    gap: BrutalistTheme.spacing.sm,
  },
  inputContainer: {
    marginBottom: BrutalistTheme.spacing.xs,
  },
  input: {
    borderWidth: 1,
    borderColor: 'rgba(88, 86, 214, 0.2)', // Purple tinted border
    borderRadius: BrutalistTheme.borderRadius.sm,
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.md,
    fontSize: BrutalistTheme.fontSizes.body,
    fontFamily: BrutalistTheme.fonts.system,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    color: BrutalistTheme.colors.grey800,
    shadowColor: '#5856D6',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.02,
    shadowRadius: 2,
    elevation: 1,
  },
  // Button container - vertical stacked
  buttonContainer: {
    gap: BrutalistTheme.spacing.md,
    marginTop: BrutalistTheme.spacing.xs,
  },

  primaryButton: {
    backgroundColor: BrutalistTheme.colors.black,
    paddingVertical: BrutalistTheme.spacing.md,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BrutalistTheme.borderRadius.sm,
    minHeight: 48,
    alignSelf: 'stretch',
    shadowColor: BrutalistTheme.colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  primaryButtonDisabled: {
    backgroundColor: BrutalistTheme.colors.grey400,
    shadowOpacity: 0,
    elevation: 0,
  },

  // Modern divider
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: BrutalistTheme.spacing.xs,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(88, 86, 214, 0.1)', // Subtle purple tint
  },
  dividerText: {
    paddingHorizontal: BrutalistTheme.spacing.md,
    letterSpacing: 1,
    fontSize: 10,
  },

  // Forgot password link - positioned near password field
  forgotPasswordLink: {
    alignSelf: 'flex-start',
    paddingVertical: BrutalistTheme.spacing.xs,
    marginBottom: BrutalistTheme.spacing.xs,
  },

  // Social buttons container
  socialButtons: {
    flexDirection: 'row',
    gap: BrutalistTheme.spacing.sm,
  },

  // Footer - Enhanced Create Account CTA
  footer: {
    alignItems: 'center',
    paddingTop: BrutalistTheme.spacing.xl,
    paddingBottom: BrutalistTheme.spacing.md,
  },
  createAccountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: BrutalistTheme.colors.surface,
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.sm,
    borderRadius: BrutalistTheme.borderRadius.md,
    gap: BrutalistTheme.spacing.xs,
  },
  createAccountPrompt: {
    textAlign: 'center',
  },
  createAccountButton: {
    paddingVertical: BrutalistTheme.spacing.xs,
    paddingHorizontal: BrutalistTheme.spacing.xs,
  },
});
