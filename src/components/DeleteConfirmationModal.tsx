import React from 'react';
import {
  Modal,
  View,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { BrutalistText } from './BrutalistText';
import { BrutalistTheme } from '../theme/colors';

interface DeleteConfirmationModalProps {
  visible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
}

const { width: screenWidth } = Dimensions.get('window');

export const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = ({
  visible,
  onConfirm,
  onCancel,
  title,
  message,
  confirmText = 'DELETE',
  cancelText = 'CANCEL',
}) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onCancel}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onCancel}
      >
        <View style={styles.container}>
          <TouchableOpacity activeOpacity={1} onPress={() => {}}>
            <View style={styles.modal}>
              {/* Header */}
              <View style={styles.header}>
                <BrutalistText
                  variant="callout"
                  weight="bold"
                  fontFamily="mono"
                  color="grey800"
                  style={styles.title}
                >
                  {title}
                </BrutalistText>
              </View>

              {/* Message */}
              <View style={styles.content}>
                <BrutalistText
                  variant="body"
                  weight="medium"
                  fontFamily="serif"
                  color="grey700"
                  style={styles.message}
                >
                  {message}
                </BrutalistText>
              </View>

              {/* Actions */}
              <View style={styles.actions}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={onCancel}
                  activeOpacity={0.8}
                >
                  <BrutalistText
                    variant="body"
                    weight="medium"
                    fontFamily="mono"
                    color="grey700"
                  >
                    {cancelText}
                  </BrutalistText>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.confirmButton}
                  onPress={onConfirm}
                  activeOpacity={0.8}
                >
                  <BrutalistText
                    variant="body"
                    weight="bold"
                    fontFamily="mono"
                    color="white"
                  >
                    {confirmText}
                  </BrutalistText>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: BrutalistTheme.spacing.lg,
  },
  container: {
    width: '100%',
    maxWidth: 320,
    alignItems: 'center',
  },
  modal: {
    backgroundColor: BrutalistTheme.colors.white,
    borderWidth: 2,
    borderColor: BrutalistTheme.colors.grey800,
    width: '100%',
    shadowColor: BrutalistTheme.colors.grey800,
    shadowOffset: {
      width: 4,
      height: 4,
    },
    shadowOpacity: 1,
    shadowRadius: 0,
    elevation: 8,
  },
  header: {
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingTop: BrutalistTheme.spacing.lg,
    paddingBottom: BrutalistTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
    alignItems: 'center',
  },
  title: {
    textAlign: 'center',
    letterSpacing: 1,
  },
  content: {
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingVertical: BrutalistTheme.spacing.lg,
    alignItems: 'center',
  },
  message: {
    textAlign: 'center',
    lineHeight: 22,
  },
  actions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: BrutalistTheme.colors.grey200,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: BrutalistTheme.spacing.md,
    alignItems: 'center',
    borderRightWidth: 1,
    borderRightColor: BrutalistTheme.colors.grey200,
    backgroundColor: BrutalistTheme.colors.white,
  },
  confirmButton: {
    flex: 1,
    paddingVertical: BrutalistTheme.spacing.md,
    alignItems: 'center',
    backgroundColor: BrutalistTheme.colors.error || '#dc3545',
  },
});