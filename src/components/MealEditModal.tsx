import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { BrutalistText } from './BrutalistText';
import { AddFoodButton } from './AddFoodButton';
import { FoodSearchModal } from './FoodSearchModal';
import { BrutalistTheme } from '../theme/colors';
import { LoggedFood, MealSession } from '../types/food';

interface MealEditModalProps {
  visible: boolean;
  session: MealSession | null;
  onSave: (editedFood: LoggedFood) => void;
  onAddFoodToSession: (sessionId: string, food: LoggedFood) => void;
  onUpdateDishName?: (sessionId: string, dishName: string) => void;
  onCancel: () => void;
  onRescan?: () => void;
}

export const MealEditModal: React.FC<MealEditModalProps> = ({
  visible,
  session,
  onSave,
  onCancel,
  onAddFoodToSession,
  onUpdateDishName,
  onRescan,
}) => {
  const [editedFoods, setEditedFoods] = useState<LoggedFood[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [showFoodSearch, setShowFoodSearch] = useState(false);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [editingNutrition, setEditingNutrition] = useState<string | null>(null);
  const [nutritionSummaryExpanded, setNutritionSummaryExpanded] = useState(false);
  const [editedDishName, setEditedDishName] = useState<string>('');

  // Update local state when session changes - make deep copy
  useEffect(() => {
    if (session) {
      console.log(
        'Initializing editedFoods with session foods:',
        session.foods.map(f => f.name),
      );
      setEditedFoods(session.foods.map(food => ({ ...food })));
      setEditedDishName(session.dish_name || '');
    }
  }, [session]);

  const toggleFoodItem = (foodIndex: number) => {
    const newFoods = [...editedFoods];
    const food = newFoods[foodIndex];
    const originalQuantity =
      session?.foods.find(f => f.id === food.id)?.quantity || 1;
    // Toggle between 0 and original quantity
    food.quantity = food.quantity === 0 ? originalQuantity : 0;
    setEditedFoods(newFoods);
  };

  const updateQuantity = (foodIndex: number, newQuantity: string) => {
    // Allow empty string during editing
    if (newQuantity === '') {
      const newFoods = [...editedFoods];
      newFoods[foodIndex].quantity = 0;
      setEditedFoods(newFoods);
      return;
    }

    const quantity = parseFloat(newQuantity);
    if (!isNaN(quantity) && quantity >= 0) {
      const newFoods = [...editedFoods];
      newFoods[foodIndex].quantity = quantity;
      setEditedFoods(newFoods);
    }
  };

  const handleSaveAll = async () => {
    if (!session || isSaving) return;

    console.log('handleSaveAll called!');
    console.log(
      'Session foods:',
      session.foods.map(f => ({ name: f.name, quantity: f.quantity })),
    );
    console.log(
      'Edited foods:',
      editedFoods.map(f => ({ name: f.name, quantity: f.quantity })),
    );

    setIsSaving(true);

    try {
      // Update dish name if it has changed
      if (editedDishName !== (session.dish_name || '') && onUpdateDishName) {
        console.log('Updating dish name:', editedDishName);
        await onUpdateDishName(session.id, editedDishName);
      }

      // Save each modified food
      for (const food of editedFoods) {
        const originalFood = session.foods.find(f => f.id === food.id);
        console.log(
          `Checking food: ${food.name}, original: ${originalFood?.quantity}, edited: ${food.quantity}`,
        );

        if (!originalFood) {
          // This is a new food, use the dedicated add-to-session handler
          console.log('Adding new food to session:', food.name);
          await onAddFoodToSession(session.id, food);
        } else {
          // Check if any values have changed
          const hasChanges = 
            food.quantity !== originalFood.quantity ||
            food.calories !== originalFood.calories ||
            food.protein !== originalFood.protein ||
            food.carbs !== originalFood.carbs ||
            food.fat !== originalFood.fat ||
            food.fiber !== originalFood.fiber ||
            food.sugar !== originalFood.sugar ||
            food.sodium !== originalFood.sodium ||
            food.cholesterol !== originalFood.cholesterol;

          if (hasChanges) {
            console.log('Saving modified food:', food.name);
            await onSave(food);
          } else {
            console.log('No changes detected for:', food.name);
          }
        }
      }

      // Close modal after all saves are complete
      onCancel();
    } catch (error) {
      console.error('Error saving meal changes:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const getTotalCalories = () => {
    return editedFoods.reduce(
      (sum, food) => sum + food.calories * food.quantity,
      0,
    );
  };

  const getTotalNutrition = () => {
    return editedFoods.reduce(
      (totals, food) => ({
        calories: totals.calories + food.calories * food.quantity,
        protein: totals.protein + food.protein * food.quantity,
        carbs: totals.carbs + food.carbs * food.quantity,
        fat: totals.fat + food.fat * food.quantity,
        fiber: totals.fiber + (food.fiber || 0) * food.quantity,
        sugar: totals.sugar + (food.sugar || 0) * food.quantity,
        sodium: totals.sodium + (food.sodium || 0) * food.quantity,
        cholesterol:
          totals.cholesterol + (food.cholesterol || 0) * food.quantity,
      }),
      {
        calories: 0,
        protein: 0,
        carbs: 0,
        fat: 0,
        fiber: 0,
        sugar: 0,
        sodium: 0,
        cholesterol: 0,
      },
    );
  };

  const formatNutritionValue = (value: number): string => {
    const result = value.toFixed(1);
    return result.endsWith('.0') ? result.slice(0, -2) : result;
  };

  const toggleExpanded = (foodId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(foodId)) {
        newSet.delete(foodId);
      } else {
        newSet.add(foodId);
      }
      return newSet;
    });
  };

  const updateNutritionValue = (
    foodIndex: number,
    field:
      | 'calories'
      | 'protein'
      | 'carbs'
      | 'fat'
      | 'fiber'
      | 'sugar'
      | 'sodium'
      | 'cholesterol',
    value: number,
  ) => {
    const newFoods = [...editedFoods];
    newFoods[foodIndex] = { ...newFoods[foodIndex], [field]: value };
    setEditedFoods(newFoods);
  };

  const handleAddFood = (food: LoggedFood) => {
    if (isSaving) return;

    try {
      console.log(
        'handleAddFood called with:',
        food.name,
        'quantity:',
        food.quantity,
      );
      console.log(
        'Current editedFoods before adding:',
        editedFoods.map(f => f.name),
      );

      // Add the new food to the edited foods list (no immediate save)
      setEditedFoods(prev => {
        const newFoods = [...prev, food];
        console.log(
          'New editedFoods after adding:',
          newFoods.map(f => f.name),
        );
        return newFoods;
      });

      // Update dish name to "mixed items" when adding foods to an existing session
      // unless it's already "mixed items" or empty
      if (editedDishName && editedDishName.toLowerCase() !== 'mixed items') {
        setEditedDishName('mixed items');
      } else if (!editedDishName) {
        setEditedDishName('mixed items');
      }

      // Close the search modal
      setShowFoodSearch(false);
    } catch (error) {
      console.error('Error adding food from search:', error);
      throw error; // Re-throw to let modal handle error display
    }
  };

  if (!session) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onCancel}
    >
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.container}>
          {/* Clean Header - No Back Button */}
          <View style={styles.header}>
            <TouchableOpacity
              style={[styles.closeButton, isSaving && styles.disabledButton]}
              onPress={isSaving ? undefined : onCancel}
              activeOpacity={isSaving ? 1 : 0.7}
            >
              <BrutalistText
                variant="callout"
                weight="bold"
                fontFamily="mono"
                color={isSaving ? 'grey400' : 'grey700'}
              >
                ✕
              </BrutalistText>
            </TouchableOpacity>

            <View style={styles.headerContent}>
              <BrutalistText
                variant="title3"
                weight="black"
                fontFamily="system"
                color="grey800"
                style={styles.headerTitle}
              >
                EDIT
              </BrutalistText>
              <BrutalistText
                variant="caption1"
                weight="medium"
                fontFamily="mono"
                color="grey600"
              >
                {session.meal_type.toUpperCase()}
              </BrutalistText>
            </View>

            <View style={styles.headerSpacer} />
          </View>

          {/* Compact Summary - Like FoodResults */}
          <View style={styles.summarySection}>
            {/* Editable Dish Name */}
            <View style={styles.dishNameContainer}>
              <TextInput
                style={styles.dishNameInput}
                value={editedDishName}
                onChangeText={setEditedDishName}
                placeholder="Enter dish name..."
                placeholderTextColor={BrutalistTheme.colors.grey500}
                multiline={false}
                textAlign="center"
              />
            </View>

            <View style={styles.detectedItems}>
              {editedFoods.map((food, index) => (
                <React.Fragment key={`${food.id}-${index}`}>
                  <BrutalistText
                    variant="caption1"
                    weight="medium"
                    color="black"
                  >
                    {food.name}
                  </BrutalistText>
                  {index < editedFoods.length - 1 && (
                    <BrutalistText
                      variant="caption1"
                      weight="medium"
                      color="grey500"
                    >
                      {' • '}
                    </BrutalistText>
                  )}
                </React.Fragment>
              ))}
            </View>
          </View>

          {/* Expandable Nutrition Summary */}
          <View style={styles.nutritionSummarySection}>
            <TouchableOpacity
              onPress={() => setNutritionSummaryExpanded(!nutritionSummaryExpanded)}
              style={styles.nutritionSummaryRow}
              activeOpacity={0.8}
            >
              <View style={styles.nutritionSummaryInfo}>
                <BrutalistText
                  variant="caption1"
                  weight="medium"
                  fontFamily="mono"
                  color="grey700"
                  style={styles.sectionLabel}
                >
                  UPDATED NUTRITION
                </BrutalistText>

                <View style={styles.nutritionSummaryMeta}>
                  <BrutalistText
                    variant="caption1"
                    weight="medium"
                    fontFamily="mono"
                    color="grey600"
                  >
                    {editedFoods.filter(f => f.quantity > 0).length} items
                  </BrutalistText>
                  <BrutalistText
                    variant="caption1"
                    weight="medium"
                    fontFamily="mono"
                    color="grey500"
                  >
                    {' • '}
                  </BrutalistText>
                  <BrutalistText
                    variant="caption1"
                    weight="bold"
                    color="accent1"
                    fontFamily="mono"
                  >
                    {Math.round(getTotalNutrition().calories)} CALS
                  </BrutalistText>
                  <BrutalistText
                    variant="caption1"
                    weight="medium"
                    fontFamily="mono"
                    color="grey500"
                    style={styles.expandIndicator}
                  >
                    {' '}[{nutritionSummaryExpanded ? '−' : '+'}]
                  </BrutalistText>
                </View>
              </View>
            </TouchableOpacity>

            {/* Expanded Nutrition Details */}
            {nutritionSummaryExpanded && (
              <View style={styles.expandedNutritionContent}>
                {/* Primary Nutrition Row */}
                <View style={styles.nutritionGrid}>
                  <View style={styles.macroItem}>
                    <BrutalistText
                      variant="caption1"
                      weight="semibold"
                      fontFamily="mono"
                      color="accent1"
                    >
                      {Math.round(getTotalNutrition().calories)}
                    </BrutalistText>
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                    >
                      CALORIES
                    </BrutalistText>
                  </View>

                  <View style={styles.macroItem}>
                    <BrutalistText
                      variant="caption1"
                      weight="semibold"
                      fontFamily="mono"
                      color="grey800"
                    >
                      {formatNutritionValue(getTotalNutrition().protein)}g
                    </BrutalistText>
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                    >
                      PROTEIN
                    </BrutalistText>
                  </View>

                  <View style={styles.macroItem}>
                    <BrutalistText
                      variant="caption1"
                      weight="semibold"
                      fontFamily="mono"
                      color="grey800"
                    >
                      {formatNutritionValue(getTotalNutrition().carbs)}g
                    </BrutalistText>
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                    >
                      CARBS
                    </BrutalistText>
                  </View>

                  <View style={styles.macroItem}>
                    <BrutalistText
                      variant="caption1"
                      weight="semibold"
                      fontFamily="mono"
                      color="grey800"
                    >
                      {formatNutritionValue(getTotalNutrition().fat)}g
                    </BrutalistText>
                    <BrutalistText
                      variant="caption2"
                      weight="medium"
                      color="grey600"
                    >
                      FAT
                    </BrutalistText>
                  </View>
                </View>

                {/* Secondary Nutrition Row - if any values are non-zero */}
                {(getTotalNutrition().fiber > 0 ||
                  getTotalNutrition().sugar > 0 ||
                  getTotalNutrition().sodium > 0 ||
                  getTotalNutrition().cholesterol > 0) && (
                  <View style={styles.nutritionGrid}>
                    {getTotalNutrition().fiber > 0 && (
                      <View style={styles.macroItem}>
                        <BrutalistText
                          variant="caption1"
                          weight="semibold"
                          fontFamily="mono"
                          color="grey800"
                        >
                          {formatNutritionValue(getTotalNutrition().fiber)}g
                        </BrutalistText>
                        <BrutalistText
                          variant="caption2"
                          weight="medium"
                          color="grey600"
                        >
                          FIBER
                        </BrutalistText>
                      </View>
                    )}

                    {getTotalNutrition().sugar > 0 && (
                      <View style={styles.macroItem}>
                        <BrutalistText
                          variant="caption1"
                          weight="semibold"
                          fontFamily="mono"
                          color="grey800"
                        >
                          {formatNutritionValue(getTotalNutrition().sugar)}g
                        </BrutalistText>
                        <BrutalistText
                          variant="caption2"
                          weight="medium"
                          color="grey600"
                        >
                          SUGAR
                        </BrutalistText>
                      </View>
                    )}

                    {getTotalNutrition().sodium > 0 && (
                      <View style={styles.macroItem}>
                        <BrutalistText
                          variant="caption1"
                          weight="semibold"
                          fontFamily="mono"
                          color="grey800"
                        >
                          {Math.round(getTotalNutrition().sodium)}mg
                        </BrutalistText>
                        <BrutalistText
                          variant="caption2"
                          weight="medium"
                          color="grey600"
                        >
                          SODIUM
                        </BrutalistText>
                      </View>
                    )}

                    {getTotalNutrition().cholesterol > 0 && (
                      <View style={styles.macroItem}>
                        <BrutalistText
                          variant="caption1"
                          weight="semibold"
                          fontFamily="mono"
                          color="grey800"
                        >
                          {Math.round(getTotalNutrition().cholesterol)}mg
                        </BrutalistText>
                        <BrutalistText
                          variant="caption2"
                          weight="medium"
                          color="grey600"
                        >
                          CHOL
                        </BrutalistText>
                      </View>
                    )}
                  </View>
                )}
              </View>
            )}
          </View>

          {/* Food Items List - Streamlined */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
          >
            <View style={styles.foodsList}>
              <BrutalistText
                variant="caption1"
                weight="medium"
                fontFamily="mono"
                color="grey700"
                style={styles.sectionLabel}
              >
                ADJUST QUANTITIES
              </BrutalistText>

              {editedFoods.map((food, index) => {
                const isExpanded = expandedItems.has(food.id);
                return (
                  <View
                    key={`food-item-${food.id}-${index}`}
                    style={styles.foodItemContainer}
                  >
                    {/* Food Row - Like FoodResults */}
                    <View style={styles.foodRow}>
                      <TouchableOpacity
                        onPress={() => toggleFoodItem(index)}
                        style={styles.foodContent}
                        activeOpacity={0.8}
                      >
                        <View style={styles.foodInfo}>
                          <BrutalistText
                            variant="body"
                            weight="medium"
                            color={food.quantity === 0 ? 'grey500' : 'black'}
                            style={
                              food.quantity === 0
                                ? [styles.foodName, styles.strikethrough]
                                : styles.foodName
                            }
                          >
                            {food.name}
                          </BrutalistText>

                          <View style={styles.quantityDisplay}>
                            <TextInput
                              style={[
                                styles.quantityInput,
                                food.quantity === 0 &&
                                  styles.quantityInputDisabled,
                              ]}
                              value={
                                food.quantity === 0
                                  ? '0'
                                  : food.quantity.toString()
                              }
                              onChangeText={text => updateQuantity(index, text)}
                              keyboardType="numeric"
                              placeholder="0"
                              placeholderTextColor={
                                BrutalistTheme.colors.grey500
                              }
                            />
                            <BrutalistText
                              variant="caption1"
                              weight="medium"
                              fontFamily="mono"
                              color={
                                food.quantity === 0 ? 'grey400' : 'grey600'
                              }
                            >
                              ×
                            </BrutalistText>
                          </View>
                        </View>

                        {/* Selection underline - like FoodResults */}
                        {food.quantity > 0 && (
                          <View style={styles.selectionUnderline} />
                        )}
                      </TouchableOpacity>

                      <TouchableOpacity
                        onPress={() => toggleExpanded(food.id)}
                        style={styles.expandButton}
                        activeOpacity={0.6}
                      >
                        <BrutalistText
                          variant="caption1"
                          weight="medium"
                          color="grey500"
                        >
                          {isExpanded ? '−' : '+'}
                        </BrutalistText>
                      </TouchableOpacity>
                    </View>

                    {/* Expanded Content - Nutrition Details */}
                    {isExpanded && (
                      <View style={styles.expandedContent}>
                        {/* Header showing total nutrition for selected quantity */}
                        <View style={styles.nutritionHeader}>
                          <BrutalistText
                            variant="caption1"
                            weight="medium"
                            color="grey600"
                            fontFamily="mono"
                          >
                            TOTAL NUTRITION ({food.quantity}×)
                          </BrutalistText>
                        </View>

                        {/* Primary Nutrition Row - editable like FoodResults */}
                        <View style={styles.nutritionGrid}>
                          <View style={styles.macroItem}>
                            {editingNutrition === `${food.id}-calories` ? (
                              <TextInput
                                style={styles.nutritionInput}
                                value={food.calories.toString()}
                                onChangeText={text => {
                                  const num = parseFloat(text) || 0;
                                  updateNutritionValue(index, 'calories', num);
                                }}
                                onBlur={() => setEditingNutrition(null)}
                                onSubmitEditing={() =>
                                  setEditingNutrition(null)
                                }
                                keyboardType="numeric"
                                selectTextOnFocus
                                autoFocus
                                maxLength={5}
                              />
                            ) : (
                              <TouchableOpacity
                                onPress={e => {
                                  e.stopPropagation();
                                  setEditingNutrition(`${food.id}-calories`);
                                }}
                                style={styles.nutritionButton}
                                activeOpacity={0.6}
                              >
                                <BrutalistText
                                  variant="caption1"
                                  weight="semibold"
                                  fontFamily="mono"
                                  color="black"
                                >
                                  {Math.round(food.calories * food.quantity)}
                                </BrutalistText>
                              </TouchableOpacity>
                            )}
                            <BrutalistText
                              variant="caption2"
                              weight="medium"
                              color="grey600"
                            >
                              CALORIES
                            </BrutalistText>
                          </View>

                          <View style={styles.macroItem}>
                            {editingNutrition === `${food.id}-protein` ? (
                              <TextInput
                                style={styles.nutritionInput}
                                value={food.protein.toString()}
                                onChangeText={text => {
                                  const num = parseFloat(text) || 0;
                                  updateNutritionValue(index, 'protein', num);
                                }}
                                onBlur={() => setEditingNutrition(null)}
                                onSubmitEditing={() =>
                                  setEditingNutrition(null)
                                }
                                keyboardType="numeric"
                                selectTextOnFocus
                                autoFocus
                                maxLength={5}
                              />
                            ) : (
                              <TouchableOpacity
                                onPress={e => {
                                  e.stopPropagation();
                                  setEditingNutrition(`${food.id}-protein`);
                                }}
                                style={styles.nutritionButton}
                                activeOpacity={0.6}
                              >
                                <BrutalistText
                                  variant="caption1"
                                  weight="semibold"
                                  fontFamily="mono"
                                  color="black"
                                >
                                  {formatNutritionValue(
                                    food.protein * food.quantity,
                                  )}g
                                </BrutalistText>
                              </TouchableOpacity>
                            )}
                            <BrutalistText
                              variant="caption2"
                              weight="medium"
                              color="grey600"
                            >
                              PROTEIN
                            </BrutalistText>
                          </View>

                          <View style={styles.macroItem}>
                            {editingNutrition === `${food.id}-carbs` ? (
                              <TextInput
                                style={styles.nutritionInput}
                                value={food.carbs.toString()}
                                onChangeText={text => {
                                  const num = parseFloat(text) || 0;
                                  updateNutritionValue(index, 'carbs', num);
                                }}
                                onBlur={() => setEditingNutrition(null)}
                                onSubmitEditing={() =>
                                  setEditingNutrition(null)
                                }
                                keyboardType="numeric"
                                selectTextOnFocus
                                autoFocus
                                maxLength={5}
                              />
                            ) : (
                              <TouchableOpacity
                                onPress={e => {
                                  e.stopPropagation();
                                  setEditingNutrition(`${food.id}-carbs`);
                                }}
                                style={styles.nutritionButton}
                                activeOpacity={0.6}
                              >
                                <BrutalistText
                                  variant="caption1"
                                  weight="semibold"
                                  fontFamily="mono"
                                  color="black"
                                >
                                  {formatNutritionValue(
                                    food.carbs * food.quantity,
                                  )}g
                                </BrutalistText>
                              </TouchableOpacity>
                            )}
                            <BrutalistText
                              variant="caption2"
                              weight="medium"
                              color="grey600"
                            >
                              CARBS
                            </BrutalistText>
                          </View>

                          <View style={styles.macroItem}>
                            {editingNutrition === `${food.id}-fat` ? (
                              <TextInput
                                style={styles.nutritionInput}
                                value={food.fat.toString()}
                                onChangeText={text => {
                                  const num = parseFloat(text) || 0;
                                  updateNutritionValue(index, 'fat', num);
                                }}
                                onBlur={() => setEditingNutrition(null)}
                                onSubmitEditing={() =>
                                  setEditingNutrition(null)
                                }
                                keyboardType="numeric"
                                selectTextOnFocus
                                autoFocus
                                maxLength={5}
                              />
                            ) : (
                              <TouchableOpacity
                                onPress={e => {
                                  e.stopPropagation();
                                  setEditingNutrition(`${food.id}-fat`);
                                }}
                                style={styles.nutritionButton}
                                activeOpacity={0.6}
                              >
                                <BrutalistText
                                  variant="caption1"
                                  weight="semibold"
                                  fontFamily="mono"
                                  color="black"
                                >
                                  {formatNutritionValue(
                                    food.fat * food.quantity,
                                  )}g
                                </BrutalistText>
                              </TouchableOpacity>
                            )}
                            <BrutalistText
                              variant="caption2"
                              weight="medium"
                              color="grey600"
                            >
                              FAT
                            </BrutalistText>
                          </View>
                        </View>

                        {/* Secondary Nutrition Row - editable if available */}
                        {(food.fiber ||
                          food.sugar ||
                          food.sodium ||
                          food.cholesterol) && (
                          <View style={styles.nutritionGrid}>
                            {food.fiber && (
                              <View style={styles.macroItem}>
                                {editingNutrition === `${food.id}-fiber` ? (
                                  <TextInput
                                    style={styles.nutritionInput}
                                    value={food.fiber.toString()}
                                    onChangeText={text => {
                                      const num = parseFloat(text) || 0;
                                      updateNutritionValue(index, 'fiber', num);
                                    }}
                                    onBlur={() => setEditingNutrition(null)}
                                    onSubmitEditing={() =>
                                      setEditingNutrition(null)
                                    }
                                    keyboardType="numeric"
                                    selectTextOnFocus
                                    autoFocus
                                    maxLength={5}
                                  />
                                ) : (
                                  <TouchableOpacity
                                    onPress={e => {
                                      e.stopPropagation();
                                      setEditingNutrition(`${food.id}-fiber`);
                                    }}
                                    style={styles.nutritionButton}
                                    activeOpacity={0.6}
                                  >
                                    <BrutalistText
                                      variant="caption1"
                                      weight="semibold"
                                      fontFamily="mono"
                                      color="black"
                                    >
                                      {formatNutritionValue(
                                        food.fiber * food.quantity,
                                      )}g
                                    </BrutalistText>
                                  </TouchableOpacity>
                                )}
                                <BrutalistText
                                  variant="caption2"
                                  weight="medium"
                                  color="grey600"
                                >
                                  FIBER
                                </BrutalistText>
                              </View>
                            )}

                            {food.sugar && (
                              <View style={styles.macroItem}>
                                {editingNutrition === `${food.id}-sugar` ? (
                                  <TextInput
                                    style={styles.nutritionInput}
                                    value={food.sugar.toString()}
                                    onChangeText={text => {
                                      const num = parseFloat(text) || 0;
                                      updateNutritionValue(index, 'sugar', num);
                                    }}
                                    onBlur={() => setEditingNutrition(null)}
                                    onSubmitEditing={() =>
                                      setEditingNutrition(null)
                                    }
                                    keyboardType="numeric"
                                    selectTextOnFocus
                                    autoFocus
                                    maxLength={5}
                                  />
                                ) : (
                                  <TouchableOpacity
                                    onPress={e => {
                                      e.stopPropagation();
                                      setEditingNutrition(`${food.id}-sugar`);
                                    }}
                                    style={styles.nutritionButton}
                                    activeOpacity={0.6}
                                  >
                                    <BrutalistText
                                      variant="caption1"
                                      weight="semibold"
                                      fontFamily="mono"
                                      color="black"
                                    >
                                      {formatNutritionValue(
                                        food.sugar * food.quantity,
                                      )}g
                                    </BrutalistText>
                                  </TouchableOpacity>
                                )}
                                <BrutalistText
                                  variant="caption2"
                                  weight="medium"
                                  color="grey600"
                                >
                                  SUGAR
                                </BrutalistText>
                              </View>
                            )}

                            {food.sodium && (
                              <View style={styles.macroItem}>
                                {editingNutrition === `${food.id}-sodium` ? (
                                  <TextInput
                                    style={styles.nutritionInput}
                                    value={food.sodium.toString()}
                                    onChangeText={text => {
                                      const num = parseFloat(text) || 0;
                                      updateNutritionValue(
                                        index,
                                        'sodium',
                                        num,
                                      );
                                    }}
                                    onBlur={() => setEditingNutrition(null)}
                                    onSubmitEditing={() =>
                                      setEditingNutrition(null)
                                    }
                                    keyboardType="numeric"
                                    selectTextOnFocus
                                    autoFocus
                                    maxLength={5}
                                  />
                                ) : (
                                  <TouchableOpacity
                                    onPress={e => {
                                      e.stopPropagation();
                                      setEditingNutrition(`${food.id}-sodium`);
                                    }}
                                    style={styles.nutritionButton}
                                    activeOpacity={0.6}
                                  >
                                    <BrutalistText
                                      variant="caption1"
                                      weight="semibold"
                                      fontFamily="mono"
                                      color="black"
                                    >
                                      {Math.round(food.sodium * food.quantity)}mg
                                    </BrutalistText>
                                  </TouchableOpacity>
                                )}
                                <BrutalistText
                                  variant="caption2"
                                  weight="medium"
                                  color="grey600"
                                >
                                  SODIUM
                                </BrutalistText>
                              </View>
                            )}

                            {food.cholesterol && (
                              <View style={styles.macroItem}>
                                {editingNutrition ===
                                `${food.id}-cholesterol` ? (
                                  <TextInput
                                    style={styles.nutritionInput}
                                    value={food.cholesterol.toString()}
                                    onChangeText={text => {
                                      const num = parseFloat(text) || 0;
                                      updateNutritionValue(
                                        index,
                                        'cholesterol',
                                        num,
                                      );
                                    }}
                                    onBlur={() => setEditingNutrition(null)}
                                    onSubmitEditing={() =>
                                      setEditingNutrition(null)
                                    }
                                    keyboardType="numeric"
                                    selectTextOnFocus
                                    autoFocus
                                    maxLength={5}
                                  />
                                ) : (
                                  <TouchableOpacity
                                    onPress={e => {
                                      e.stopPropagation();
                                      setEditingNutrition(
                                        `${food.id}-cholesterol`,
                                      );
                                    }}
                                    style={styles.nutritionButton}
                                    activeOpacity={0.6}
                                  >
                                    <BrutalistText
                                      variant="caption1"
                                      weight="semibold"
                                      fontFamily="mono"
                                      color="black"
                                    >
                                      {Math.round(
                                        food.cholesterol * food.quantity,
                                      )}mg
                                    </BrutalistText>
                                  </TouchableOpacity>
                                )}
                                <BrutalistText
                                  variant="caption2"
                                  weight="medium"
                                  color="grey600"
                                >
                                  CHOL
                                </BrutalistText>
                              </View>
                            )}
                          </View>
                        )}
                      </View>
                    )}
                  </View>
                );
              })}

              {/* Add Food Section - Like FoodResults */}
              <View style={styles.addFoodSection}>
                {onRescan && (
                  <TouchableOpacity
                    onPress={onRescan}
                    style={styles.secondaryActionButton}
                    activeOpacity={0.6}
                    disabled={isSaving}
                  >
                    <BrutalistText
                      variant="body"
                      weight="medium"
                      color={isSaving ? 'grey500' : 'grey700'}
                      fontFamily="mono"
                      style={styles.secondaryActionText}
                    >
                      + RESCAN
                    </BrutalistText>
                  </TouchableOpacity>
                )}
                
                <TouchableOpacity
                  onPress={() => setShowFoodSearch(true)}
                  style={styles.secondaryActionButton}
                  activeOpacity={0.6}
                  disabled={isSaving}
                >
                  <BrutalistText
                    variant="body"
                    weight="medium"
                    color={isSaving ? 'grey500' : 'grey700'}
                    fontFamily="mono"
                    style={styles.secondaryActionText}
                  >
                    + SEARCH
                  </BrutalistText>
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>

          {/* Action Buttons - Like FoodResults */}
          <View style={styles.actions}>
            {/* Primary Action Buttons */}
            <View style={styles.primaryActionButtons}>
              <TouchableOpacity
                onPress={onCancel}
                style={styles.retakeAction}
                activeOpacity={0.7}
                disabled={isSaving}
              >
                <BrutalistText
                  variant="body"
                  weight="medium"
                  color={isSaving ? 'grey400' : 'grey700'}
                  fontFamily="system"
                >
                  Cancel
                </BrutalistText>
              </TouchableOpacity>

              {/* Selection Status */}
              <View style={styles.selectionStatus}>
                <View style={styles.selectionStatusRow}>
                  <BrutalistText
                    variant="caption1"
                    weight="medium"
                    color="grey600"
                    fontFamily="mono"
                  >
                    {editedFoods.filter(f => f.quantity > 0).length}/
                    {editedFoods.length}
                  </BrutalistText>
                  {editedFoods.filter(f => f.quantity > 0).length > 0 && (
                    <BrutalistText
                      variant="caption1"
                      weight="bold"
                      color="accent1"
                      fontFamily="mono"
                    >
                      {' '}
                      + {Math.round(getTotalCalories())} CALS
                    </BrutalistText>
                  )}
                </View>
              </View>

              <TouchableOpacity
                onPress={handleSaveAll}
                style={[styles.logAction, isSaving && styles.disabledAction]}
                activeOpacity={0.7}
                disabled={isSaving}
              >
                <View style={styles.logButtonContent}>
                  {isSaving && (
                    <ActivityIndicator
                      size="small"
                      color={BrutalistTheme.colors.grey600}
                      style={styles.loadingIndicator}
                    />
                  )}
                  <BrutalistText
                    variant="body"
                    weight="medium"
                    color={isSaving ? 'grey500' : 'black'}
                    fontFamily="system"
                  >
                    {isSaving ? 'Saving...' : 'Save'}
                  </BrutalistText>
                </View>
                <View
                  style={[
                    styles.logActionUnderline,
                    isSaving && styles.disabledUnderline,
                  ]}
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>

      {/* Food Search Modal */}
      <FoodSearchModal
        visible={showFoodSearch}
        mealType={session?.meal_type || 'snack'}
        onAddFood={handleAddFood}
        onCancel={() => setShowFoodSearch(false)}
      />
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: BrutalistTheme.colors.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingTop: BrutalistTheme.spacing.lg,
    paddingBottom: BrutalistTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
  },
  closeButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
  headerContent: {
    alignItems: 'center',
    gap: 4,
  },
  headerTitle: {
    letterSpacing: 1,
  },
  headerSpacer: {
    width: 44,
  },

  // Compact Summary Section - Like FoodResults
  summarySection: {
    backgroundColor: BrutalistTheme.colors.white,
    paddingVertical: BrutalistTheme.spacing.sm,
    paddingHorizontal: BrutalistTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xs,
  },
  dishName: {
    marginBottom: BrutalistTheme.spacing.xs,
  },
  dishNameContainer: {
    width: '100%',
    marginBottom: BrutalistTheme.spacing.xs,
  },
  dishNameInput: {
    fontSize: 14,
    fontFamily: BrutalistTheme.fonts.system,
    fontWeight: '500',
    color: BrutalistTheme.colors.grey600,
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.xs,
    borderWidth: 0,
    backgroundColor: 'transparent',
    textAlign: 'center',
  },
  detectedItems: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Enhanced Nutrition Summary - Expandable
  nutritionSummarySection: {
    backgroundColor: BrutalistTheme.colors.white,
    paddingVertical: BrutalistTheme.spacing.sm,
    paddingHorizontal: BrutalistTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: BrutalistTheme.colors.grey200,
  },
  nutritionSummaryRow: {
    paddingVertical: BrutalistTheme.spacing.xs,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nutritionSummaryInfo: {
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xs,
  },
  nutritionSummaryMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  expandIndicator: {
    marginLeft: 2,
  },
  expandedNutritionContent: {
    paddingTop: BrutalistTheme.spacing.sm,
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xs,
  },
  sectionLabel: {
    textAlign: 'center',
    letterSpacing: 1,
    marginBottom: BrutalistTheme.spacing.xs,
  },

  // Clean Nutrition Grid Layout
  nutritionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: BrutalistTheme.spacing.xs,
  },
  macroItem: {
    alignItems: 'center',
    flex: 1,
    gap: 1,
  },

  content: {
    flex: 1,
  },
  foodsList: {
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingTop: BrutalistTheme.spacing.sm,
    paddingBottom: BrutalistTheme.spacing.lg,
    gap: 0,
  },

  // Food Items - Like FoodResults
  foodItemContainer: {
    marginBottom: 0,
  },
  foodRow: {
    backgroundColor: BrutalistTheme.colors.white,
    paddingHorizontal: BrutalistTheme.spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 44,
  },
  foodContent: {
    flex: 1,
    paddingVertical: BrutalistTheme.spacing.sm,
  },
  foodInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  foodName: {
    flex: 1,
    marginRight: BrutalistTheme.spacing.sm,
  },
  quantityDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xs,
  },
  quantityInput: {
    fontSize: 14,
    fontWeight: 'medium',
    fontFamily: BrutalistTheme.fonts.mono,
    textAlign: 'center',
    minWidth: 32,
    paddingHorizontal: BrutalistTheme.spacing.xs,
    paddingVertical: 2,
    borderWidth: 0,
    backgroundColor: 'transparent',
    color: BrutalistTheme.colors.grey600,
  },
  quantityInputDisabled: {
    color: BrutalistTheme.colors.grey400,
  },
  strikethrough: {
    textDecorationLine: 'line-through',
  },
  selectionUnderline: {
    height: 3,
    backgroundColor: BrutalistTheme.colors.grey600,
    marginTop: 2,
    borderRadius: 0,
  },
  expandButton: {
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 40,
  },

  // Expanded Content - Like FoodResults
  expandedContent: {
    paddingTop: BrutalistTheme.spacing.sm,
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingBottom: BrutalistTheme.spacing.md,
    backgroundColor: BrutalistTheme.colors.white,
  },
  nutritionHeader: {
    alignItems: 'center',
    marginBottom: BrutalistTheme.spacing.sm,
  },
  nutritionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: BrutalistTheme.spacing.xs,
    paddingVertical: 2,
    position: 'relative',
  },
  nutritionInput: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: BrutalistTheme.fonts.mono,
    textAlign: 'center',
    minWidth: 40,
    paddingHorizontal: BrutalistTheme.spacing.xs,
    paddingVertical: 2,
    borderWidth: 0,
    backgroundColor: 'transparent',
    color: BrutalistTheme.colors.black,
  },

  // Add Food Section
  addFoodSection: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: BrutalistTheme.spacing.md,
    paddingBottom: BrutalistTheme.spacing.sm,
    gap: BrutalistTheme.spacing.xl,
  },
  secondaryActionButton: {
    paddingVertical: BrutalistTheme.spacing.sm,
    paddingHorizontal: BrutalistTheme.spacing.sm,
  },
  secondaryActionText: {
    letterSpacing: 0.8,
    fontSize: 14,
  },

  // Clean Footer - Like FoodResults
  actions: {
    backgroundColor: BrutalistTheme.colors.white,
    paddingHorizontal: BrutalistTheme.spacing.lg,
    paddingTop: BrutalistTheme.spacing.sm,
    paddingBottom: BrutalistTheme.spacing.lg,
    borderTopWidth: 1,
    borderTopColor: BrutalistTheme.colors.grey200,
  },
  primaryActionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  retakeAction: {
    paddingVertical: 0,
    paddingHorizontal: BrutalistTheme.spacing.xs,
  },
  selectionStatus: {
    alignItems: 'center',
    flex: 1,
  },
  selectionStatusRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logAction: {
    alignItems: 'flex-end',
    paddingVertical: 0,
  },
  logButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: BrutalistTheme.spacing.xs,
  },
  loadingIndicator: {
    marginRight: 2,
  },
  logActionUnderline: {
    height: 2,
    backgroundColor: BrutalistTheme.colors.grey700,
    marginTop: 2,
    width: '100%',
  },
  disabledAction: {
    opacity: 0.5,
  },
  disabledUnderline: {
    backgroundColor: BrutalistTheme.colors.grey400,
  },
});
