import React, {
  useState,
  useMemo,
  useRef,
  useCallback,
  useEffect,
} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  Alert,
} from 'react-native';
import { BrutalistText } from './BrutalistText';
import { BrutalistView } from './BrutalistView';
import { SwipeableItem, SwipeableItemRef } from './SwipeableItem';
import { DeleteConfirmationModal } from './DeleteConfirmationModal';
import { AppHeader } from './AppHeader';
import { BrutalistTheme } from '../theme/colors';
import { MealSession, LoggedFood } from '../types/food';
import { foodLogService } from '../services/foodLogService';
import { weeklyDataService } from '../services/weeklyDataService';

interface HistoryScreenProps {
  mealSessions: MealSession[];
  userId: string;
  onDeleteSession?: (sessionId: string) => void;
  onDeleteFoodItem?: (foodId: string, sessionId: string) => void;
  onEditMealSession?: (session: MealSession) => void;
  onDataChanged?: () => void;
}

interface GroupedSessions {
  [date: string]: MealSession[];
}

// Week utility functions
const getWeekStart = (date: Date): Date => {
  const d = new Date(date);
  d.setHours(0, 0, 0, 0); // Set to start of day for consistent comparison
  const day = d.getDay();
  const diff = d.getDate() - day;
  return new Date(d.setDate(diff));
};

const getWeekEnd = (date: Date): Date => {
  const weekStart = getWeekStart(date);
  const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);
  // Set to end of day (23:59:59.999) to include all meals from the last day
  weekEnd.setHours(23, 59, 59, 999);
  return weekEnd;
};

const formatWeekRange = (weekStart: Date): string => {
  const weekEnd = getWeekEnd(weekStart);
  const startMonth = weekStart.toLocaleDateString('en-US', { month: 'short' });
  const endMonth = weekEnd.toLocaleDateString('en-US', { month: 'short' });
  const startDay = weekStart.getDate();
  const endDay = weekEnd.getDate();
  const year = weekStart.getFullYear();

  if (startMonth === endMonth) {
    return `${startMonth} ${startDay}-${endDay}, ${year}`;
  } else {
    return `${startMonth} ${startDay} - ${endMonth} ${endDay}, ${year}`;
  }
};

const isSameWeek = (date1: Date, date2: Date): boolean => {
  const week1Start = getWeekStart(date1);
  const week2Start = getWeekStart(date2);
  return week1Start.getTime() === week2Start.getTime();
};

const isCurrentWeek = (weekStart: Date): boolean => {
  return isSameWeek(weekStart, new Date());
};

export const HistoryScreen: React.FC<HistoryScreenProps> = ({
  mealSessions,
  userId,
  onDeleteSession,
  onDeleteFoodItem,
  onEditMealSession,
  onDataChanged,
}) => {
  const [expandedDates, setExpandedDates] = useState<Set<string>>(new Set());
  const [expandedSessions, setExpandedSessions] = useState<Set<string>>(
    new Set(),
  );
  const [selectedWeekStart, setSelectedWeekStart] = useState<Date>(
    getWeekStart(new Date()),
  );
  const [showCalendar, setShowCalendar] = useState(false);
  const [calendarViewDate, setCalendarViewDate] = useState<Date>(new Date());
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    visible: boolean;
    type: 'session' | 'food';
    sessionId?: string;
    foodId?: string;
    title: string;
    message: string;
  } | null>(null);
  const [openSwipeItemId, setOpenSwipeItemId] = useState<string | null>(null);
  const [loadingWeeks, setLoadingWeeks] = useState<Set<string>>(new Set());
  const [weeksWithDataFromServer, setWeeksWithDataFromServer] = useState<
    Set<string>
  >(new Set());
  const swipeableRefs = useRef<Map<string, SwipeableItemRef>>(new Map());

  // Filter meal sessions by selected week and group by date
  const filteredAndGroupedSessions = useMemo(() => {
    const weekEnd = getWeekEnd(selectedWeekStart);

    const filteredSessions = mealSessions.filter(session => {
      const sessionDate = new Date(session.logged_at);
      // Normalize session date to start of day for consistent comparison
      sessionDate.setHours(0, 0, 0, 0);
      return sessionDate >= selectedWeekStart && sessionDate <= weekEnd;
    });

    const grouped: GroupedSessions = {};
    filteredSessions.forEach(session => {
      // Use consistent local date string for grouping
      const sessionDate = new Date(session.logged_at);
      const date = sessionDate.toDateString();
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(session);
    });

    return grouped;
  }, [mealSessions, selectedWeekStart]);

  const dates = Object.keys(filteredAndGroupedSessions).sort(
    (a, b) => new Date(b).getTime() - new Date(a).getTime(),
  );

  // Load week data when selectedWeekStart changes
  useEffect(() => {
    const loadWeekIfNeeded = async () => {
      if (!weeklyDataService.isWeekCached(userId, selectedWeekStart)) {
        const weekKey = selectedWeekStart.toISOString().split('T')[0];

        // Add week to loading state
        setLoadingWeeks(prev => new Set(prev).add(weekKey));

        try {
          console.log(`Loading data for week: ${weekKey}`);
          const result = await weeklyDataService.downloadWeekData(
            userId,
            selectedWeekStart,
          );

          if (result.success) {
            console.log(`Successfully loaded week ${weekKey}`);
            // Trigger data refresh to show newly loaded data
            if (onDataChanged) {
              onDataChanged();
            }
          } else {
            console.error(`Failed to load week ${weekKey}:`, result.error);
          }
        } catch (error) {
          console.error(`Error loading week ${weekKey}:`, error);
        } finally {
          // Remove week from loading state
          setLoadingWeeks(prev => {
            const newSet = new Set(prev);
            newSet.delete(weekKey);
            return newSet;
          });
        }
      }
    };

    loadWeekIfNeeded();
  }, [selectedWeekStart, userId, onDataChanged]);

  // Load weeks with data for calendar indicators (only once on mount)
  useEffect(() => {
    const loadWeeksWithData = async () => {
      try {
        const result = await weeklyDataService.getWeeksWithData(userId);
        if (result.success && result.weeks) {
          setWeeksWithDataFromServer(new Set(result.weeks));
        } else {
          console.error('Failed to load weeks with data:', result.error);
        }
      } catch (error) {
        console.error('Error loading weeks with data:', error);
      }
    };

    loadWeeksWithData();
  }, [userId]);

  // Week navigation functions
  const goToPreviousWeek = () => {
    const previousWeek = new Date(
      selectedWeekStart.getTime() - 7 * 24 * 60 * 60 * 1000,
    );
    setSelectedWeekStart(getWeekStart(previousWeek));
  };

  const goToNextWeek = () => {
    const nextWeek = new Date(
      selectedWeekStart.getTime() + 7 * 24 * 60 * 60 * 1000,
    );
    setSelectedWeekStart(getWeekStart(nextWeek));
  };

  const openCalendar = () => {
    // Set calendar view to the month of the currently selected week
    setCalendarViewDate(new Date(selectedWeekStart));
    setShowCalendar(true);
  };

  const closeCalendar = () => {
    setShowCalendar(false);
  };

  // Get all weeks that have meal data (combines local data + server data)
  const weeksWithData = useMemo(() => {
    const weeks = new Set<string>();

    // Add weeks from local meal sessions
    mealSessions.forEach(session => {
      const sessionDate = new Date(session.logged_at);
      const weekStart = getWeekStart(sessionDate);
      weeks.add(weekStart.toISOString().split('T')[0]);
    });

    // Add weeks from server data (for calendar indicators)
    weeksWithDataFromServer.forEach(weekKey => {
      weeks.add(weekKey);
    });

    return weeks;
  }, [mealSessions, weeksWithDataFromServer]);

  // Generate calendar weeks for the current month view
  const calendarWeeks = useMemo(() => {
    const weeks = [];
    const viewMonth = calendarViewDate.getMonth();
    const viewYear = calendarViewDate.getFullYear();

    // Get first day of the month
    const firstOfMonth = new Date(viewYear, viewMonth, 1);
    // Get last day of the month
    const lastOfMonth = new Date(viewYear, viewMonth + 1, 0);

    // Find the week that contains the first day of the month
    let currentWeekStart = getWeekStart(firstOfMonth);

    // Generate weeks until we've covered the entire month
    while (currentWeekStart <= lastOfMonth || weeks.length < 6) {
      const weekStartKey = currentWeekStart.toISOString().split('T')[0];
      const currentDate = new Date();

      weeks.push({
        weekStart: new Date(currentWeekStart),
        hasData: weeksWithData.has(weekStartKey),
        isCurrent: isSameWeek(currentWeekStart, currentDate),
        isSelected: isSameWeek(currentWeekStart, selectedWeekStart),
      });

      // Move to next week
      currentWeekStart = new Date(
        currentWeekStart.getTime() + 7 * 24 * 60 * 60 * 1000,
      );

      // Stop if we have enough weeks and have passed the month
      if (weeks.length >= 6 && currentWeekStart.getMonth() !== viewMonth) {
        break;
      }
    }

    return weeks;
  }, [weeksWithData, selectedWeekStart, calendarViewDate]);

  const selectWeek = (weekStart: Date) => {
    setSelectedWeekStart(weekStart);
    closeCalendar();
  };

  // Month navigation for calendar
  const goToPreviousMonth = () => {
    const previousMonth = new Date(calendarViewDate);
    previousMonth.setMonth(previousMonth.getMonth() - 1);
    setCalendarViewDate(previousMonth);
  };

  const goToNextMonth = () => {
    const nextMonth = new Date(calendarViewDate);
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    setCalendarViewDate(nextMonth);
  };

  const goToCurrentMonth = () => {
    setCalendarViewDate(new Date());
  };

  // Deletion handlers
  const handleDeleteFood = (
    foodId: string,
    sessionId: string,
    foodName: string,
  ) => {
    setDeleteConfirmation({
      visible: true,
      type: 'food',
      sessionId,
      foodId,
      title: 'DELETE FOOD ITEM',
      message: `Are you sure you want to delete "${foodName}" from this meal?`,
    });
  };

  const handleDeleteSession = (
    sessionId: string,
    mealType: string,
    itemCount: number,
  ) => {
    setDeleteConfirmation({
      visible: true,
      type: 'session',
      sessionId,
      title: 'DELETE MEAL SESSION',
      message: `Are you sure you want to delete this entire ${mealType} session (${itemCount} items)?`,
    });
  };

  const confirmDeletion = async () => {
    if (!deleteConfirmation) return;

    try {
      if (deleteConfirmation.type === 'food' && deleteConfirmation.foodId) {
        const { error } = await foodLogService.deleteFoodItem(
          userId,
          deleteConfirmation.foodId,
        );
        if (error) throw error;

        if (onDeleteFoodItem && deleteConfirmation.sessionId) {
          onDeleteFoodItem(
            deleteConfirmation.foodId,
            deleteConfirmation.sessionId,
          );
        }
      } else if (
        deleteConfirmation.type === 'session' &&
        deleteConfirmation.sessionId
      ) {
        const { error } = await foodLogService.deleteMealSession(
          userId,
          deleteConfirmation.sessionId,
        );
        if (error) throw error;

        if (onDeleteSession) {
          onDeleteSession(deleteConfirmation.sessionId);
        }
      }

      // Refresh data
      if (onDataChanged) {
        onDataChanged();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to delete item. Please try again.');
      console.error('Delete error:', error);
    }

    setDeleteConfirmation(null);
  };

  const cancelDeletion = () => {
    setDeleteConfirmation(null);
  };

  // Swipe management handlers
  const handleSwipeStart = (itemId: string) => {
    // Close any currently open item
    if (openSwipeItemId && openSwipeItemId !== itemId) {
      const currentOpenRef = swipeableRefs.current.get(openSwipeItemId);
      if (currentOpenRef) {
        currentOpenRef.reset();
      }
    }
    setOpenSwipeItemId(itemId);
  };

  const handleSwipeReset = () => {
    setOpenSwipeItemId(null);
  };

  const setSwipeableRef = (itemId: string, ref: SwipeableItemRef | null) => {
    if (ref) {
      swipeableRefs.current.set(itemId, ref);
    } else {
      swipeableRefs.current.delete(itemId);
    }
  };

  const toggleDateExpanded = (date: string) => {
    setExpandedDates(prev => {
      const newSet = new Set(prev);
      if (newSet.has(date)) {
        newSet.delete(date);
      } else {
        newSet.add(date);
      }
      return newSet;
    });
  };

  const toggleSessionExpanded = (sessionId: string) => {
    setExpandedSessions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sessionId)) {
        newSet.delete(sessionId);
      } else {
        newSet.add(sessionId);
      }
      return newSet;
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date().toDateString();
    const yesterday = new Date(Date.now() - 86400000).toDateString();
    const isCurrentWeekSelected = isSameWeek(selectedWeekStart, new Date());

    const actualDate = date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });

    let dayLabel = '';
    let showActualDate = true; // Always show actual date for consistency

    if (isCurrentWeekSelected) {
      if (dateString === today) {
        dayLabel = 'Today';
      } else if (dateString === yesterday) {
        dayLabel = 'Yesterday';
      } else {
        // Other days in current week - just show weekday
        dayLabel = date.toLocaleDateString('en-US', {
          weekday: 'long',
        });
      }
    } else {
      // Other weeks - show weekday for consistency with current week
      dayLabel = date.toLocaleDateString('en-US', {
        weekday: 'long',
      });
    }

    return { actualDate, dayLabel, showActualDate };
  };

  const getDayTotals = (sessions: MealSession[]) => {
    const allFoods = sessions.flatMap(session => session.foods);
    return {
      calories: allFoods.reduce(
        (sum, food) => sum + food.calories * food.quantity,
        0,
      ),
      protein: allFoods.reduce(
        (sum, food) => sum + food.protein * food.quantity,
        0,
      ),
      carbs: allFoods.reduce(
        (sum, food) => sum + food.carbs * food.quantity,
        0,
      ),
      fat: allFoods.reduce((sum, food) => sum + food.fat * food.quantity, 0),
      sessionCount: sessions.length,
    };
  };

  const formatNutritionValue = (value: number): string => {
    const result = value.toFixed(1);
    return result.endsWith('.0') ? result.slice(0, -2) : result;
  };

  const renderSessionItem = (session: MealSession) => {
    const isExpanded = expandedSessions.has(session.id);

    return (
      <SwipeableItem
        key={session.id}
        ref={ref => setSwipeableRef(`session-${session.id}`, ref)}
        itemId={`session-${session.id}`}
        onDelete={() =>
          handleDeleteSession(
            session.id,
            session.meal_type,
            session.total_items,
          )
        }
        onEdit={
          onEditMealSession ? () => onEditMealSession(session) : undefined
        }
        onSwipeStart={handleSwipeStart}
        onSwipeReset={handleSwipeReset}
        deleteText="DELETE"
        editText="EDIT"
        deleteThreshold={40}
      >
        <TouchableOpacity
          style={styles.sessionItem}
          onPress={
            session.total_items > 1
              ? () => toggleSessionExpanded(session.id)
              : undefined
          }
          activeOpacity={session.total_items > 1 ? 0.7 : 1}
        >
          {/* Session Header */}
          <View style={styles.sessionHeader}>
            <View style={styles.sessionInfo}>
              <View style={styles.timeAndMealGroup}>
                <BrutalistText
                  variant="caption1"
                  weight="medium"
                  color="grey600"
                  fontFamily="mono"
                  style={styles.timeLabel}
                >
                  {new Date(session.logged_at).toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </BrutalistText>
                <BrutalistText variant="body" weight="regular" color="grey800">
                  {session.meal_type}
                </BrutalistText>
                {session.dish_name && (
                  <BrutalistText
                    variant="footnote"
                    color="accent1"
                    fontFamily="serif"
                    style={styles.dishName}
                  >
                    {session.dish_name}
                  </BrutalistText>
                )}
              </View>
              <View>
                <BrutalistText
                  variant="caption1"
                  weight="medium"
                  color={isExpanded ? 'accent2' : 'grey600'}
                >
                  {session.total_items === 1
                    ? `${session.foods[0]?.name || 'Item'} • ${
                        session.foods[0]?.quantity || 1
                      }×`
                    : `${session.total_items} items${isExpanded ? ' −' : ' +'}`}
                </BrutalistText>
              </View>
            </View>

            <BrutalistText
              variant="callout"
              weight="semibold"
              color="accent1"
              fontFamily="mono"
            >
              {Math.round(session.total_calories)}
            </BrutalistText>
          </View>

          {/* Expanded Individual Foods */}
          {isExpanded && (
            <View style={styles.expandedFoods}>
              {session.foods.map((food, index) => (
                <SwipeableItem
                  key={`${food.id}-${index}`}
                  ref={ref => setSwipeableRef(`food-${food.id}-${index}`, ref)}
                  itemId={`food-${food.id}-${index}`}
                  onDelete={() =>
                    handleDeleteFood(food.id, session.id, food.name)
                  }
                  onSwipeStart={handleSwipeStart}
                  onSwipeReset={handleSwipeReset}
                  deleteText="DEL"
                  editText="EDIT"
                  deleteThreshold={40}
                >
                  <View style={styles.foodItemRow}>
                    <BrutalistText
                      variant="caption1"
                      weight="medium"
                      color="grey600"
                    >
                      {food.name} • {food.quantity}×
                    </BrutalistText>
                  </View>
                </SwipeableItem>
              ))}
            </View>
          )}
        </TouchableOpacity>
      </SwipeableItem>
    );
  };

  const renderDateSection = (date: string, index: number) => {
    const sessions = filteredAndGroupedSessions[date];
    const totals = getDayTotals(sessions);
    const isExpanded = expandedDates.has(date);
    const isAlternate = index % 2 === 1;

    return (
      <View key={date} style={styles.dateSection}>
        {/* Date Header */}
        <TouchableOpacity
          style={[styles.dateHeader, isAlternate && styles.dateHeaderAlternate]}
          onPress={() => toggleDateExpanded(date)}
          activeOpacity={0.7}
        >
          <View style={styles.dateHeaderContent}>
            <View style={styles.dateInfo}>
              {(() => {
                const dateInfo = formatDate(date);
                return (
                  <>
                    {dateInfo.showActualDate && (
                      <BrutalistText
                        variant="caption1"
                        weight="medium"
                        color="grey600"
                        style={styles.actualDateLabel}
                        fontFamily="mono"
                      >
                        {dateInfo.actualDate}
                      </BrutalistText>
                    )}
                    <BrutalistText
                      variant="body"
                      color="black"
                      style={styles.dateLabel}
                    >
                      {dateInfo.dayLabel}
                    </BrutalistText>
                  </>
                );
              })()}
              <BrutalistText
                variant="caption1"
                weight="medium"
                color={isExpanded ? 'accent2' : 'grey700'}
                style={styles.mealCountLabel}
              >
                {totals.sessionCount} meals {isExpanded ? '−' : '+'}
              </BrutalistText>
            </View>

            <View style={styles.dateTotals}>
              <View style={styles.macrosRow}>
                <View style={styles.macroItem}>
                  <BrutalistText
                    variant="caption1"
                    weight="semibold"
                    fontFamily="mono"
                    color="accent1"
                  >
                    {Math.round(totals.calories)}
                  </BrutalistText>
                  <BrutalistText
                    variant="caption2"
                    weight="medium"
                    color="grey700"
                    style={styles.macroLabel}
                  >
                    cal
                  </BrutalistText>
                </View>

                <View style={styles.macroItem}>
                  <BrutalistText
                    variant="caption1"
                    weight="semibold"
                    fontFamily="mono"
                    color="grey800"
                  >
                    {formatNutritionValue(totals.protein)}g
                  </BrutalistText>
                  <BrutalistText
                    variant="caption2"
                    weight="medium"
                    color="grey700"
                    style={styles.macroLabel}
                  >
                    protein
                  </BrutalistText>
                </View>

                <View style={styles.macroItem}>
                  <BrutalistText
                    variant="caption1"
                    weight="semibold"
                    fontFamily="mono"
                    color="grey800"
                  >
                    {formatNutritionValue(totals.carbs)}g
                  </BrutalistText>
                  <BrutalistText
                    variant="caption2"
                    weight="medium"
                    color="grey700"
                    style={styles.macroLabel}
                  >
                    carbs
                  </BrutalistText>
                </View>

                <View style={styles.macroItem}>
                  <BrutalistText
                    variant="caption1"
                    weight="semibold"
                    fontFamily="mono"
                    color="grey800"
                  >
                    {formatNutritionValue(totals.fat)}g
                  </BrutalistText>
                  <BrutalistText
                    variant="caption2"
                    weight="medium"
                    color="grey700"
                    style={styles.macroLabel}
                  >
                    fat
                  </BrutalistText>
                </View>
              </View>
            </View>
          </View>
        </TouchableOpacity>

        {/* Expanded Content - Shows Meal Sessions Only */}
        {isExpanded && (
          <View
            style={[
              styles.expandedContent,
              isAlternate && styles.expandedContentAlternate,
            ]}
          >
            {/* Meal Sessions List */}
            <View style={styles.sessionsList}>
              {sessions.map(renderSessionItem)}
            </View>
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Weekly Summary - Always at top */}
      {dates.length > 0 &&
        (() => {
          const weekSessions = Object.values(filteredAndGroupedSessions).flat();
          return (
            <BrutalistView
              variant="floating-attached"
              padding="md"
              style={{ marginBottom: BrutalistTheme.spacing.sm }}
            >
              <BrutalistText
                variant="tabLabelMedium"
                color="grey700"
                style={styles.summaryHeader}
              >
                weekly summary
              </BrutalistText>

              <View style={styles.historyMetrics}>
                <View style={styles.metricItem}>
                  <BrutalistText
                    variant="headline"
                    weight="bold"
                    fontFamily="mono"
                    color="grey800"
                  >
                    {weekSessions.length}
                  </BrutalistText>
                  <BrutalistText
                    variant="caption2"
                    weight="medium"
                    color="grey700"
                  >
                    MEALS
                  </BrutalistText>
                </View>

                <View style={styles.metricItem}>
                  <BrutalistText
                    variant="headline"
                    weight="bold"
                    fontFamily="mono"
                    color="grey800"
                  >
                    {dates.length}
                  </BrutalistText>
                  <BrutalistText
                    variant="caption2"
                    weight="medium"
                    color="grey700"
                  >
                    DAYS
                  </BrutalistText>
                </View>

                <View style={styles.metricItem}>
                  <BrutalistText
                    variant="headline"
                    weight="bold"
                    fontFamily="mono"
                    color="accent1"
                  >
                    {Math.round(
                      weekSessions.reduce(
                        (sum, session) => sum + session.total_calories,
                        0,
                      ),
                    )}
                  </BrutalistText>
                  <BrutalistText
                    variant="caption2"
                    weight="medium"
                    color="grey700"
                  >
                    TOTAL CALS
                  </BrutalistText>
                </View>
              </View>
            </BrutalistView>
          );
        })()}

      {/* Week Navigation */}
      <View style={styles.weekNavigationContainer}>
        <View style={styles.weekNavigation}>
          <TouchableOpacity
            style={styles.weekNavButton}
            onPress={goToPreviousWeek}
            activeOpacity={0.7}
          >
            <BrutalistText variant="tabLabel" color="grey700">
              ← prev
            </BrutalistText>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.weekRangeButton}
            onPress={openCalendar}
            activeOpacity={0.7}
          >
            <BrutalistText
              variant="h6"
              weight="semibold"
              fontFamily="system"
              color={isCurrentWeek(selectedWeekStart) ? 'accent1' : 'grey800'}
              style={styles.weekRangeText}
            >
              {formatWeekRange(selectedWeekStart)}
            </BrutalistText>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.weekNavButton}
            onPress={goToNextWeek}
            activeOpacity={0.7}
          >
            <BrutalistText variant="tabLabel" color="grey700">
              next →
            </BrutalistText>
          </TouchableOpacity>
        </View>
      </View>

      {/* Loading indicator for week data */}
      {loadingWeeks.has(selectedWeekStart.toISOString().split('T')[0]) && (
        <View style={styles.loadingContainer}>
          <BrutalistText
            variant="bodySmall"
            color="grey700"
            style={styles.loadingText}
          >
            loading week data...
          </BrutalistText>
        </View>
      )}

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          {dates.length === 0 ? (
            <View style={styles.emptyState}>
              <BrutalistText
                variant="h3"
                color="grey700"
                style={styles.emptyMessage}
              >
                No food entries yet
              </BrutalistText>
              <BrutalistText
                variant="bodySmall"
                color="grey600"
                style={styles.emptySubMessage}
              >
                Start scanning food to build your nutrition history
              </BrutalistText>
            </View>
          ) : (
            dates.map((date, index) => renderDateSection(date, index))
          )}
        </View>
      </ScrollView>

      {/* Calendar Modal */}
      <Modal
        visible={showCalendar}
        transparent={true}
        animationType="fade"
        onRequestClose={closeCalendar}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={closeCalendar}
        >
          <View style={styles.calendarContainer}>
            <View style={styles.calendarHeader}>
              <BrutalistText
                variant="callout"
                weight="bold"
                fontFamily="mono"
                color="grey800"
              >
                SELECT WEEK
              </BrutalistText>
              <TouchableOpacity onPress={closeCalendar}>
                <BrutalistText variant="h4" color="grey700">
                  ✕
                </BrutalistText>
              </TouchableOpacity>
            </View>

            {/* Month Navigation */}
            <View style={styles.monthNavigation}>
              <TouchableOpacity
                style={styles.monthNavButton}
                onPress={goToPreviousMonth}
                activeOpacity={0.7}
              >
                <BrutalistText variant="tabLabel" color="grey700">
                  ← prev
                </BrutalistText>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.monthDisplayButton}
                onPress={goToCurrentMonth}
                activeOpacity={0.7}
              >
                <BrutalistText variant="h6" color="accent1">
                  {calendarViewDate.toLocaleDateString('en-US', {
                    month: 'long',
                    year: 'numeric',
                  })}
                </BrutalistText>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.monthNavButton}
                onPress={goToNextMonth}
                activeOpacity={0.7}
              >
                <BrutalistText variant="tabLabel" color="grey700">
                  next →
                </BrutalistText>
              </TouchableOpacity>
            </View>

            <ScrollView
              style={styles.calendarWeeks}
              showsVerticalScrollIndicator={false}
            >
              {calendarWeeks.map(week => (
                <TouchableOpacity
                  key={week.weekStart.toISOString()}
                  style={[
                    styles.calendarWeekItem,
                    week.isSelected && styles.calendarWeekSelected,
                    week.isCurrent && styles.calendarWeekCurrent,
                  ]}
                  onPress={() => selectWeek(week.weekStart)}
                  activeOpacity={0.7}
                >
                  <View style={styles.calendarWeekContent}>
                    <BrutalistText
                      variant="caption1"
                      weight={week.isSelected ? 'bold' : 'medium'}
                      fontFamily="mono"
                      color="grey800"
                    >
                      {formatWeekRange(week.weekStart)}
                    </BrutalistText>
                    {week.hasData && (
                      <View
                        style={[
                          styles.dataIndicator,
                          week.isSelected && styles.dataIndicatorSelected,
                        ]}
                      />
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Delete Confirmation Modal */}
      {deleteConfirmation && (
        <DeleteConfirmationModal
          visible={deleteConfirmation.visible}
          title={deleteConfirmation.title}
          message={deleteConfirmation.message}
          onConfirm={confirmDeletion}
          onCancel={cancelDeletion}
          confirmText="DELETE"
          cancelText="CANCEL"
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(248, 248, 252, 1)', // Subtle purple-tinted background
  },
  scrollView: {
    flex: 1,
  },

  // Week Navigation
  weekNavigationContainer: {
    backgroundColor: 'rgba(248, 248, 252, 1)', // Match app background
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(88, 86, 214, 0.06)', // Subtle purple tinted divider
  },
  weekNavigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.xs,
  },
  weekNavButton: {
    paddingHorizontal: BrutalistTheme.spacing.sm,
    paddingVertical: BrutalistTheme.spacing.xs,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: BrutalistTheme.borderRadius.sm,
    minWidth: 60,
    alignItems: 'center',
    shadowColor: '#5856D6',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.02,
    shadowRadius: 4,
    elevation: 1,
  },
  weekRangeButton: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: BrutalistTheme.spacing.sm,
    paddingVertical: BrutalistTheme.spacing.xs,
    marginHorizontal: BrutalistTheme.spacing.sm,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: BrutalistTheme.borderRadius.sm,
    gap: 1,
    shadowColor: '#5856D6',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.02,
    shadowRadius: 4,
    elevation: 1,
  },
  weekRangeText: {
    textAlign: 'center',
    letterSpacing: 0.5,
  },

  // Content
  content: {
    flex: 1,
    paddingTop: BrutalistTheme.spacing.sm,
    paddingBottom: BrutalistTheme.spacing.lg,
    gap: 0,
  },

  // Overall Summary Section - matches Today screen
  overallSummarySection: {
    backgroundColor: 'rgba(255, 255, 255, 0.96)',
    paddingTop: BrutalistTheme.spacing.sm,
    paddingBottom: BrutalistTheme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(88, 86, 214, 0.06)',
    alignItems: 'center',
    gap: BrutalistTheme.spacing.sm,
  },
  summaryHeader: {
    textAlign: 'center',
    letterSpacing: 1,
    marginBottom: 5,
    paddingHorizontal: BrutalistTheme.spacing.md,
  },
  historyMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: BrutalistTheme.spacing.md,
  },
  metricItem: {
    alignItems: 'center',
    flex: 1,
    gap: 1,
  },

  // Date Section
  dateSection: {
    marginBottom: 0,
    shadowColor: '#5856D6',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.02,
    shadowRadius: 4,
    elevation: 1,
  },
  dateHeader: {
    backgroundColor: 'rgba(255, 252, 248, 1)', // Warm cream tint
    paddingVertical: BrutalistTheme.spacing.sm,
    paddingHorizontal: BrutalistTheme.spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 2, // Increased from 1 to 2
    borderBottomColor: 'rgba(88, 86, 214, 0.08)', // Slightly stronger divider
    borderLeftWidth: 3,
    borderLeftColor: 'rgba(255, 149, 0, 0.4)', // Warm orange accent line
  },
  dateHeaderAlternate: {
    backgroundColor: 'rgba(248, 252, 250, 1)', // Cool green tint
    borderLeftColor: 'rgba(52, 199, 89, 0.4)', // Cool accent line
    shadowOpacity: 0.04, // Slightly stronger shadow for alternates
    shadowRadius: 6,
    elevation: 2,
  },
  dateHeaderContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dateInfo: {
    flex: 1,
  },
  actualDateLabel: {
    letterSpacing: 0.5,
    marginBottom: -4,
  },
  dateLabel: {
    letterSpacing: 0.5,
  },
  mealCountLabel: {
    marginTop: -2,

    letterSpacing: 0.2,
  },
  dateTotals: {
    alignItems: 'flex-end',
    marginRight: BrutalistTheme.spacing.md,
    flex: 1,
  },
  macrosRow: {
    flexDirection: 'row',
    gap: BrutalistTheme.spacing.md,
    alignItems: 'flex-end',
  },
  macroItem: {
    alignItems: 'center',
    minWidth: 32,
  },
  macroLabel: {
    marginTop: -2,
    fontSize: 9,
    letterSpacing: 0.2,
  },

  // Expanded Content
  expandedContent: {
    backgroundColor: 'rgba(255, 252, 248, 1)', // Match primary date header
    borderLeftWidth: 3,
    borderLeftColor: 'rgba(255, 149, 0, 0.4)', // Warm orange accent line
  },
  expandedContentAlternate: {
    backgroundColor: 'rgba(248, 252, 250, 1)', // Match alternate date header
    borderLeftWidth: 3,
    borderLeftColor: 'rgba(52, 199, 89, 0.4)', // Cool accent line
  },

  // Daily Summary
  dailySummary: {
    backgroundColor: 'rgba(255, 255, 255, 0.96)',
    paddingVertical: BrutalistTheme.spacing.md,
    paddingHorizontal: BrutalistTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(88, 86, 214, 0.06)',
  },

  macroGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  // Meal Sessions List
  sessionsList: {
    gap: 0,
  },

  // Session Item
  sessionItem: {
    backgroundColor: 'transparent',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(88, 86, 214, 0.06)', // Subtle purple tinted divider

    marginBottom: 0,
    paddingVertical: BrutalistTheme.spacing.xs,
  },
  sessionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: BrutalistTheme.spacing.md,
  },
  sessionInfo: {
    flex: 1,
    gap: 1,
  },
  timeAndMealGroup: {
    gap: 1,
  },
  sessionTotals: {
    alignItems: 'flex-end',
    gap: 1,
    marginRight: BrutalistTheme.spacing.md,
  },
  expandedFoods: {
    paddingBottom: 1,
    paddingLeft: BrutalistTheme.spacing.md,
    gap: 0,
  },
  foodItemRow: {
    paddingVertical: 0,
    backgroundColor: 'transparent',
  },
  timeLabel: {
    letterSpacing: 0.5,
    marginBottom: -4,
  },

  // Foods List (individual foods within sessions)
  foodsList: {
    padding: BrutalistTheme.spacing.md,
    gap: BrutalistTheme.spacing.xs,
  },
  foodItem: {
    paddingVertical: BrutalistTheme.spacing.sm,
  },
  foodContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  foodMainInfo: {
    flex: 1,
    gap: 2,
  },
  foodNutrition: {
    alignItems: 'flex-end',
    gap: 1,
  },
  foodUnderline: {
    height: 1,
    backgroundColor: 'rgba(88, 86, 214, 0.06)',
    marginTop: BrutalistTheme.spacing.xs,
  },

  // Empty State
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: BrutalistTheme.spacing.xxxl,
    gap: BrutalistTheme.spacing.md,
  },
  emptyMessage: {
    textAlign: 'center',
  },
  emptySubMessage: {
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: BrutalistTheme.spacing.lg,
  },

  // Calendar Modal
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: BrutalistTheme.spacing.lg,
  },
  calendarContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    borderRadius: BrutalistTheme.borderRadius.md,
    width: '100%',
    maxWidth: 340,
    maxHeight: '80%',
    shadowColor: '#5856D6',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 8,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(88, 86, 214, 0.06)',
  },
  calendarWeeks: {
    paddingBottom: BrutalistTheme.spacing.sm,
  },
  calendarWeekItem: {
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(88, 86, 214, 0.06)',
  },
  calendarWeekSelected: {
    backgroundColor: BrutalistTheme.colors.grey200,
  },
  calendarWeekCurrent: {
    // No background color
  },
  calendarWeekContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dataIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: BrutalistTheme.colors.accent2,
  },
  dataIndicatorSelected: {
    backgroundColor: BrutalistTheme.colors.accent1,
  },

  // Month Navigation
  monthNavigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(88, 86, 214, 0.06)',
  },
  monthNavButton: {
    paddingHorizontal: BrutalistTheme.spacing.sm,
    paddingVertical: BrutalistTheme.spacing.xs,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: BrutalistTheme.borderRadius.sm,
    minWidth: 60,
    alignItems: 'center',
    shadowColor: '#5856D6',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.02,
    shadowRadius: 4,
    elevation: 1,
  },
  monthDisplayButton: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: BrutalistTheme.spacing.md,
    paddingVertical: BrutalistTheme.spacing.xs,
    marginHorizontal: BrutalistTheme.spacing.sm,
  },
  dishName: {
    marginTop: 0,
    fontStyle: 'italic',
    lineHeight: 14,
    marginBottom: 1,
  },

  // Loading indicator
  loadingContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.96)',
    paddingVertical: BrutalistTheme.spacing.sm,
    paddingHorizontal: BrutalistTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(88, 86, 214, 0.06)',
    alignItems: 'center',
  },
  loadingText: {
    letterSpacing: 1,
  },
});
