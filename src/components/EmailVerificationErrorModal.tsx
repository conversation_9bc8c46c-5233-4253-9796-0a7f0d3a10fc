import React from 'react';
import {
  View,
  StyleSheet,
  Modal,
  TouchableOpacity,
} from 'react-native';
import { BrutalistText } from './index';
import { BrutalistTheme } from '../theme/colors';

interface EmailVerificationErrorModalProps {
  visible: boolean;
  onClose: () => void;
  errorCode?: string;
  errorMessage?: string;
}

export const EmailVerificationErrorModal: React.FC<EmailVerificationErrorModalProps> = ({
  visible,
  onClose,
  errorCode,
  errorMessage,
}) => {
  // No auto-dismiss - user controls when to close

  // Map error codes to user-friendly messages
  const getErrorMessage = () => {
    if (errorMessage) return errorMessage;
    
    switch (errorCode) {
      case 'otp_expired':
        return 'Your email verification link has expired. Please request a new verification email.';
      case 'access_denied':
        return 'This email verification link is invalid or has already been used.';
      default:
        return 'Email verification failed. Please try signing up again or contact support.';
    }
  };

  const getErrorTitle = () => {
    switch (errorCode) {
      case 'otp_expired':
        return 'Link Expired';
      case 'access_denied':
        return 'Invalid Link';
      default:
        return 'Verification Failed';
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modal}>

          {/* Error Message */}
          <View style={styles.content}>
            <BrutalistText
              variant="title3"
              weight="bold"
              color="grey900"
              fontFamily="system"
              style={styles.title}
            >
              {getErrorTitle()}
            </BrutalistText>

            <BrutalistText
              variant="body"
              color="grey700"
              fontFamily="system"
              style={styles.message}
            >
              {getErrorMessage()}
            </BrutalistText>


            {errorCode && (
              <BrutalistText
                variant="caption2"
                color="grey400"
                fontFamily="mono"
                style={styles.errorCode}
              >
                Error: {errorCode}
              </BrutalistText>
            )}
          </View>

          {/* Close Button */}
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            activeOpacity={0.7}
          >
            <BrutalistText
              variant="callout"
              weight="bold"
              color="white"
              fontFamily="system"
            >
              Close
            </BrutalistText>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: BrutalistTheme.spacing.lg,
  },
  modal: {
    backgroundColor: BrutalistTheme.colors.white,
    borderRadius: BrutalistTheme.borderRadius.md,
    padding: BrutalistTheme.spacing.xl,
    alignItems: 'center',
    maxWidth: 340,
    width: '100%',
    borderWidth: 1,
    borderColor: BrutalistTheme.colors.grey200,
  },
  content: {
    alignItems: 'center',
    marginBottom: BrutalistTheme.spacing.xl,
    gap: BrutalistTheme.spacing.sm,
  },
  title: {
    textAlign: 'center',
  },
  message: {
    textAlign: 'center',
    lineHeight: 22,
  },
  errorCode: {
    textAlign: 'center',
    letterSpacing: 0.5,
    marginTop: BrutalistTheme.spacing.xs,
    fontStyle: 'italic',
  },
  closeButton: {
    backgroundColor: BrutalistTheme.colors.black,
    paddingVertical: BrutalistTheme.spacing.md,
    paddingHorizontal: BrutalistTheme.spacing.xl,
    borderRadius: BrutalistTheme.borderRadius.sm,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    borderWidth: 1,
    borderColor: BrutalistTheme.colors.black,
  },
});