import React from 'react';
import { View, StyleSheet, Image, Dimensions } from 'react-native';
import { BrutalistText } from './BrutalistText';
import { BrutalistTheme } from '../theme/colors';

export const CameraHeader: React.FC = () => {
  return (
    <View style={styles.header}>
      <BrutalistText variant="h5" color="white" style={styles.appName}>
        KOA
      </BrutalistText>

      <Image
        source={require('../../assets/koa.png')}
        style={styles.logo}
        resizeMode="contain"
      />

      <View style={styles.placeholder} />
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    position: 'absolute',
    top: 60, // Account for status bar + safe area
    left: 0,
    right: 0,
    height: 60,
    zIndex: 1000,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: BrutalistTheme.spacing.lg,
    backgroundColor: 'rgba(0, 0, 0, 0.3)', // Semi-transparent background
  },
  appName: {
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
    fontSize: 18,
    fontWeight: '300',
  },
  logo: {
    width: 40,
    height: 40,
    position: 'absolute',
    left: (Dimensions.get('window').width - 40) / 2,
    top: 10,
    tintColor: 'white',
    opacity: 1,
  },
  placeholder: {
    width: 40,
    height: 40,
  },
});
