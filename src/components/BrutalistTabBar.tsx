import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { BrutalistText } from './BrutalistText';
import { BrutalistTheme } from '../theme/colors';
import { Camera, Calendar, History } from 'lucide-react-native';

export type TabKey = 'scan' | 'today' | 'history';

interface Tab {
  key: TabKey;
  label: string;
  icon: React.ComponentType<{ color: string; size: number }>;
}

interface BrutalistTabBarProps {
  activeTab: TabKey;
  onTabPress: (tab: TabKey) => void;
}

const tabs: Tab[] = [
  { key: 'scan', label: 'scan', icon: Camera },
  { key: 'today', label: 'today', icon: Calendar },
  { key: 'history', label: 'history', icon: History },
];

export const BrutalistTabBar: React.FC<BrutalistTabBarProps> = ({
  activeTab,
  onTabPress,
}) => {
  return (
    <View style={styles.container}>
      {tabs.map(tab => {
        const isActive = activeTab === tab.key;
        const IconComponent = tab.icon;

        return (
          <TouchableOpacity
            key={tab.key}
            style={styles.tab}
            onPress={() => onTabPress(tab.key)}
            activeOpacity={0.7}
          >
            <IconComponent color={isActive ? '#000000' : '#6B7280'} size={24} />
            <BrutalistText
              variant="tabLabel"
              color={isActive ? 'black' : 'grey600'}
              style={
                isActive
                  ? { ...styles.tabLabel, ...styles.activeTabLabel }
                  : styles.tabLabel
              }
            >
              {tab.label}
            </BrutalistText>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: 'rgba(248, 248, 252, 1)', // Match app background
    paddingTop: BrutalistTheme.spacing.sm,
    paddingBottom: 0,
    paddingHorizontal: BrutalistTheme.spacing.md,

    borderTopWidth: BrutalistTheme.borderWidth.thin,
    borderTopColor: 'rgba(88, 86, 214, 0.06)', // Subtle purple tinted divider
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 0,
    paddingBottom: 0,
    gap: 3,
  },
  tabLabel: {
    textAlign: 'center',
    letterSpacing: 1.0,
    fontSize: 14,
    lineHeight: 20,
    includeFontPadding: false,
  },
  activeTabLabel: {
    fontSize: 14,
    letterSpacing: 1.0,
  },
});
