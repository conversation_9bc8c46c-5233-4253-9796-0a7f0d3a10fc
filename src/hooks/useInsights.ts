import { useState, useEffect, useCallback, useMemo } from 'react'
import { insightsService, InsightsData, DailyInsight } from '../services/insightsService'

interface UseInsightsResult {
  insights: DailyInsight[]
  currentInsight: DailyInsight | null
  loading: boolean
  error: string | null
  refresh: () => Promise<void>
}

export const useInsights = (userId: string | undefined): UseInsightsResult => {
  const [insightsData, setInsightsData] = useState<InsightsData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchInsights = useCallback(async () => {
    if (!userId) return

    setLoading(true)
    setError(null)

    try {
      const { data, error: fetchError } = await insightsService.getInsights(userId)
      
      if (fetchError) {
        setError('Failed to load insights')
        console.error('Insights fetch error:', fetchError)
      } else {
        setInsightsData(data)
      }
    } catch (err) {
      setError('Failed to load insights')
      console.error('Insights error:', err)
    } finally {
      setLoading(false)
    }
  }, [userId])

  // Initial load
  useEffect(() => {
    fetchInsights()
  }, [fetchInsights])

  // Auto-refresh every 2 minutes
  useEffect(() => {
    if (!userId) return

    const interval = setInterval(() => {
      fetchInsights()
    }, 2 * 60 * 1000) // 2 minutes

    return () => clearInterval(interval)
  }, [fetchInsights, userId])

  const insights = insightsData?.insights || []
  
  // Force re-render every 20 seconds to update rotating insight
  const [refreshCounter, setRefreshCounter] = useState(0)
  useEffect(() => {
    const interval = setInterval(() => {
      setRefreshCounter(prev => prev + 1)
    }, 10 * 1000) // 10 seconds

    return () => clearInterval(interval)
  }, [])
  
  const currentInsight = useMemo(() => 
    insightsService.getRotatingInsight(insights), 
    [insights, refreshCounter]
  )

  return {
    insights,
    currentInsight,
    loading,
    error,
    refresh: fetchInsights
  }
}