import { useEffect, useRef, useState } from 'react';
import { Animated } from 'react-native';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';

interface StaggeredAnimationOptions {
  itemCount: number;
  staggerDelay?: number;
  enableHaptics?: boolean;
  animationDuration?: number;
}

export const useStaggeredAnimation = ({
  itemCount,
  staggerDelay = 150,
  enableHaptics = true,
  animationDuration = 400,
}: StaggeredAnimationOptions) => {
  const [animatedValues, setAnimatedValues] = useState<Animated.Value[]>([]);
  const hasAnimated = useRef(false);

  // Initialize animated values for each item
  useEffect(() => {
    setAnimatedValues(
      Array(itemCount)
        .fill(0)
        .map(() => new Animated.Value(0)),
    );
    // Allow animation to re-run when the item count changes
    hasAnimated.current = false;
  }, [itemCount]);

  const startAnimation = () => {
    if (hasAnimated.current) return;
    hasAnimated.current = true;

    // Create staggered animations
    const animations = animatedValues.map((animValue, index) =>
      Animated.timing(animValue, {
        toValue: 1,
        duration: animationDuration,
        delay: index * staggerDelay,
        useNativeDriver: true,
      }),
    );

    // Start all animations
    Animated.parallel(animations).start();

    // Add haptic feedback for each card appearance
    if (enableHaptics) {
      animatedValues.forEach((_, index) => {
        setTimeout(() => {
          ReactNativeHapticFeedback.trigger('impactLight');
        }, index * staggerDelay);
      });

      // Final haptic when all cards are loaded
      setTimeout(() => {
        ReactNativeHapticFeedback.trigger('impactMedium');
      }, (itemCount - 1) * staggerDelay + animationDuration);
    }
  };

  const resetAnimation = () => {
    hasAnimated.current = false;
    animatedValues.forEach(animValue => {
      animValue.setValue(0);
    });
  };

  // Get animation styles for a specific item
  const getItemStyle = (index: number) => {
    const animValue = animatedValues[index];
    if (!animValue) return {};

    return {
      opacity: animValue,
      transform: [
        {
          scale: animValue.interpolate({
            inputRange: [0, 1],
            outputRange: [0.8, 1],
          }),
        },
        {
          translateY: animValue.interpolate({
            inputRange: [0, 1],
            outputRange: [20, 0],
          }),
        },
      ],
    };
  };

  return {
    startAnimation,
    resetAnimation,
    getItemStyle,
    animatedValues: animatedValues,
  };
};