import { useState, useCallback, useRef } from 'react';
import { lidarMeasurementService, MeasurementResult, MeasurementError } from '../services/lidarMeasurementService';

interface UseLidarMeasurementResult {
  // State
  isSupported: boolean | null;
  isMeasuring: boolean;
  lastMeasurement: MeasurementResult | null;
  error: string | null;
  
  // Actions
  initializeLidar: () => Promise<boolean>;
  measureDistance: () => Promise<MeasurementResult | null>;
  clearError: () => void;
  clearLastMeasurement: () => void;
}

export function useLidarMeasurement(): UseLidarMeasurementResult {
  const [isSupported, setIsSupported] = useState<boolean | null>(null);
  const [isMeasuring, setIsMeasuring] = useState(false);
  const [lastMeasurement, setLastMeasurement] = useState<MeasurementResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const initializationPromise = useRef<Promise<boolean> | null>(null);
  
  const initializeLidar = useCallback(async (): Promise<boolean> => {
    // Prevent multiple simultaneous initialization calls
    if (initializationPromise.current) {
      return initializationPromise.current;
    }
    
    console.log('🚀 [useLidarMeasurement] Initializing LiDAR support check...');
    setError(null);
    
    initializationPromise.current = (async () => {
      try {
        const supported = await lidarMeasurementService.isLidarSupported();
        setIsSupported(supported);
        console.log(`✅ [useLidarMeasurement] LiDAR support: ${supported ? 'YES' : 'NO'}`);
        return supported;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to check LiDAR support';
        setError(errorMessage);
        console.error('❌ [useLidarMeasurement] Initialization error:', err);
        setIsSupported(false);
        return false;
      } finally {
        // Clear the promise reference after completion
        initializationPromise.current = null;
      }
    })();
    
    return initializationPromise.current;
  }, []);
  
  const measureDistance = useCallback(async (): Promise<MeasurementResult | null> => {
    console.log('📏 [useLidarMeasurement] Starting distance measurement...');
    
    if (isMeasuring) {
      console.log('⚠️ [useLidarMeasurement] Measurement already in progress');
      return null;
    }
    
    if (isSupported === null) {
      console.log('🔄 [useLidarMeasurement] LiDAR support not checked, initializing...');
      const supported = await initializeLidar();
      if (!supported) {
        setError('LiDAR not supported on this device');
        return null;
      }
    }
    
    if (!isSupported) {
      setError('LiDAR measurements not available on this device');
      return null;
    }
    
    setIsMeasuring(true);
    setError(null);
    
    try {
      console.log('🎯 [useLidarMeasurement] Performing measurement...');
      const result = await lidarMeasurementService.measureDistance();
      
      setLastMeasurement(result);
      console.log('✅ [useLidarMeasurement] Measurement successful:', {
        distance: result.distance,
        confidence: result.confidence,
        quality: result.quality
      });
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Distance measurement failed';
      setError(errorMessage);
      console.error('❌ [useLidarMeasurement] Measurement error:', err);
      return null;
    } finally {
      setIsMeasuring(false);
      console.log('🏁 [useLidarMeasurement] Measurement process complete');
    }
  }, [isSupported, isMeasuring, initializeLidar]);
  
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  const clearLastMeasurement = useCallback(() => {
    setLastMeasurement(null);
  }, []);
  
  return {
    isSupported,
    isMeasuring,
    lastMeasurement,
    error,
    initializeLidar,
    measureDistance,
    clearError,
    clearLastMeasurement
  };
}