import { useEffect, useRef, useState } from 'react';
import { Animated } from 'react-native';

interface CounterAnimationOptions {
  targetValue: number;
  duration?: number;
  delay?: number;
  precision?: number;
}

export const useCounterAnimation = ({
  targetValue,
  duration = 800,
  delay = 0,
  precision = 0,
}: CounterAnimationOptions) => {
  const [displayValue, setDisplayValue] = useState(0);
  const animatedValue = useRef(new Animated.Value(0));
  const hasAnimated = useRef(false);

  useEffect(() => {
    // Listen to animated value changes
    const listener = animatedValue.current.addListener(({ value }) => {
      const roundedValue = precision > 0 
        ? Math.round(value * Math.pow(10, precision)) / Math.pow(10, precision)
        : Math.round(value);
      setDisplayValue(roundedValue);
    });

    return () => {
      animatedValue.current.removeListener(listener);
    };
  }, [precision]);

  const startAnimation = () => {
    if (hasAnimated.current) return;
    hasAnimated.current = true;

    setTimeout(() => {
      Animated.timing(animatedValue.current, {
        toValue: targetValue,
        duration,
        useNativeDriver: false, // Can't use native driver for value interpolation
      }).start();
    }, delay);
  };

  const resetAnimation = () => {
    hasAnimated.current = false;
    animatedValue.current.setValue(0);
    setDisplayValue(0);
  };

  return {
    displayValue,
    startAnimation,
    resetAnimation,
  };
};