import { useState, useEffect } from 'react'
import { syncService } from '../services/syncService'

export const useSyncStatus = () => {
  const [syncStatus, setSyncStatus] = useState({ isPending: false, pendingCount: 0 })

  useEffect(() => {
    // Initial status
    updateStatus()

    // Poll for status changes every few seconds
    const interval = setInterval(() => {
      updateStatus()
    }, 3000)

    return () => clearInterval(interval)
  }, [])

  const updateStatus = () => {
    const status = syncService.getSyncStatus()
    setSyncStatus(status)
  }

  const triggerSync = async () => {
    await syncService.triggerSync()
    updateStatus() // Refresh status after sync
  }

  const clearSyncQueue = () => {
    syncService.clearSyncQueue()
    updateStatus()
  }

  return {
    ...syncStatus,
    triggerSync,
    clearSyncQueue,
    refreshStatus: updateStatus
  }
}