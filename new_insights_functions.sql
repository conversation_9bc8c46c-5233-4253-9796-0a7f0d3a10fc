-- New Supabase RPC Functions for Enhanced Insights
-- These integrate cleanly with the existing insights system

-- 1. MEAL TIMING ANALYSIS
-- Analyzes eating window and meal timing patterns
CREATE OR REPLACE FUNCTION calculate_meal_timing_insights(
    p_user_id UUID,
    p_days_back INTEGER DEFAULT 7
)
RETURNS JSON AS $$
DECLARE
    result JSON;
    eating_window_hours NUMERIC;
    avg_first_meal_hour NUMERIC;
    avg_last_meal_hour NUMERIC;
    meal_count INTEGER;
BEGIN
    -- Calculate eating window and meal timing patterns
    WITH daily_meal_times AS (
        SELECT 
            DATE(logged_at) as meal_date,
            MIN(EXTRACT(EPOCH FROM logged_at::time) / 3600) as first_meal_hour,
            MAX(EXTRACT(EPOCH FROM logged_at::time) / 3600) as last_meal_hour,
            COUNT(DISTINCT meal_session_id) as daily_meals
        FROM food_logs 
        WHERE user_id = p_user_id 
        AND logged_at >= CURRENT_DATE - INTERVAL '%s days' % p_days_back
        GROUP BY DATE(logged_at)
        HAVING COUNT(*) > 0
    ),
    averages AS (
        SELECT 
            AVG(last_meal_hour - first_meal_hour) as avg_eating_window,
            AVG(first_meal_hour) as avg_first_meal,
            AVG(last_meal_hour) as avg_last_meal,
            AVG(daily_meals) as avg_meals_per_day
        FROM daily_meal_times
    )
    SELECT json_build_object(
        'eating_window_hours', ROUND(avg_eating_window::numeric, 1),
        'avg_first_meal_hour', ROUND(avg_first_meal::numeric, 1),
        'avg_last_meal_hour', ROUND(avg_last_meal::numeric, 1),
        'avg_meals_per_day', ROUND(avg_meals_per_day::numeric, 1),
        'days_analyzed', (SELECT COUNT(DISTINCT DATE(logged_at)) FROM food_logs WHERE user_id = p_user_id AND logged_at >= CURRENT_DATE - INTERVAL '%s days' % p_days_back)
    ) INTO result FROM averages;
    
    RETURN COALESCE(result, '{}'::json);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. FOOD VARIETY SCORE
-- Calculates dietary diversity based on unique foods consumed
CREATE OR REPLACE FUNCTION calculate_food_variety_score(
    p_user_id UUID,
    p_days_back INTEGER DEFAULT 7
)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    WITH food_variety AS (
        SELECT 
            COUNT(DISTINCT LOWER(TRIM(food_name))) as unique_foods_count,
            COUNT(*) as total_food_entries,
            COUNT(DISTINCT DATE(logged_at)) as days_with_data
        FROM food_logs 
        WHERE user_id = p_user_id 
        AND logged_at >= CURRENT_DATE - INTERVAL '%s days' % p_days_back
    ),
    daily_variety AS (
        SELECT 
            DATE(logged_at) as log_date,
            COUNT(DISTINCT LOWER(TRIM(food_name))) as daily_unique_foods
        FROM food_logs 
        WHERE user_id = p_user_id 
        AND logged_at >= CURRENT_DATE - INTERVAL '%s days' % p_days_back
        GROUP BY DATE(logged_at)
    )
    SELECT json_build_object(
        'total_unique_foods', fv.unique_foods_count,
        'avg_daily_variety', ROUND((SELECT AVG(daily_unique_foods) FROM daily_variety)::numeric, 1),
        'variety_score', LEAST(100, ROUND((fv.unique_foods_count::numeric / GREATEST(p_days_back, 1)) * 10, 0)),
        'days_analyzed', fv.days_with_data
    ) INTO result 
    FROM food_variety fv;
    
    RETURN COALESCE(result, '{}'::json);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. ENERGY DISTRIBUTION ANALYSIS  
-- Analyzes how calories are distributed throughout the day
CREATE OR REPLACE FUNCTION calculate_energy_distribution(
    p_user_id UUID,
    p_days_back INTEGER DEFAULT 7
)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    WITH hourly_calories AS (
        SELECT 
            EXTRACT(hour FROM logged_at) as meal_hour,
            SUM(calories * quantity) as hour_calories
        FROM food_logs 
        WHERE user_id = p_user_id 
        AND logged_at >= CURRENT_DATE - INTERVAL '%s days' % p_days_back
        GROUP BY EXTRACT(hour FROM logged_at)
    ),
    total_calories AS (
        SELECT SUM(calories * quantity) as total_cals
        FROM food_logs 
        WHERE user_id = p_user_id 
        AND logged_at >= CURRENT_DATE - INTERVAL '%s days' % p_days_back
    ),
    time_periods AS (
        SELECT 
            SUM(CASE WHEN EXTRACT(hour FROM logged_at) BETWEEN 6 AND 11 THEN calories * quantity ELSE 0 END) as morning_cals,
            SUM(CASE WHEN EXTRACT(hour FROM logged_at) BETWEEN 12 AND 17 THEN calories * quantity ELSE 0 END) as afternoon_cals,
            SUM(CASE WHEN EXTRACT(hour FROM logged_at) BETWEEN 18 AND 23 THEN calories * quantity ELSE 0 END) as evening_cals,
            SUM(calories * quantity) as total_period_cals
        FROM food_logs 
        WHERE user_id = p_user_id 
        AND logged_at >= CURRENT_DATE - INTERVAL '%s days' % p_days_back
    )
    SELECT json_build_object(
        'morning_percentage', CASE WHEN tp.total_period_cals > 0 THEN ROUND((tp.morning_cals / tp.total_period_cals * 100)::numeric, 0) ELSE 0 END,
        'afternoon_percentage', CASE WHEN tp.total_period_cals > 0 THEN ROUND((tp.afternoon_cals / tp.total_period_cals * 100)::numeric, 0) ELSE 0 END,
        'evening_percentage', CASE WHEN tp.total_period_cals > 0 THEN ROUND((tp.evening_cals / tp.total_period_cals * 100)::numeric, 0) ELSE 0 END,
        'front_loaded', CASE WHEN tp.total_period_cals > 0 THEN (tp.morning_cals + tp.afternoon_cals) > tp.evening_cals ELSE false END,
        'peak_eating_hour', (SELECT meal_hour FROM hourly_calories ORDER BY hour_calories DESC LIMIT 1)
    ) INTO result 
    FROM time_periods tp;
    
    RETURN COALESCE(result, '{}'::json);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION calculate_meal_timing_insights TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_food_variety_score TO authenticated;  
GRANT EXECUTE ON FUNCTION calculate_energy_distribution TO authenticated;