-- DEBUG: Let's check what's happening during deletion

-- First, let's see if the user actually gets deleted from auth.users
-- Run this BEFORE deletion to see current users
SELECT id, email, created_at 
FROM auth.users 
WHERE email = '<EMAIL>';  -- Replace with your test email

-- Check if refresh tokens exist for the user
SELECT user_id, token, revoked, created_at 
FROM auth.refresh_tokens 
WHERE user_id = (
  SELECT id FROM auth.users WHERE email = '<EMAIL>'
);

-- Test the function directly (run this while logged in as the user you want to delete)
SELECT delete_user_account();

-- After running delete_user_account(), check if user is gone:
SELECT id, email, created_at 
FROM auth.users 
WHERE email = '<EMAIL>';  -- Should return no rows

-- Check if refresh tokens were revoked:
SELECT user_id, token, revoked, created_at 
FROM auth.refresh_tokens 
WHERE user_id = 'USER_ID_HERE';  -- Replace with the actual user ID