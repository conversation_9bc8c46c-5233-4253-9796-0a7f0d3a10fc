{"name": "FoodScannerMobile", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@invertase/react-native-apple-authentication": "^2.4.1", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native/new-app-screen": "0.80.2", "@supabase/supabase-js": "^2.53.0", "lucide-react-native": "^0.540.0", "react": "19.1.0", "react-native": "0.80.2", "react-native-compressor": "^1.12.0", "react-native-config": "^1.5.6", "react-native-dotenv": "^3.4.11", "react-native-haptic-feedback": "^2.3.3", "react-native-keychain": "^10.0.0", "react-native-mmkv": "^3.3.0", "react-native-permissions": "^5.4.2", "react-native-svg": "^15.12.1", "react-native-url-polyfill": "^2.0.0", "react-native-uuid": "^2.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.1.1", "@react-native-community/cli-platform-android": "19.1.1", "@react-native-community/cli-platform-ios": "19.1.1", "@react-native/babel-preset": "0.80.2", "@react-native/eslint-config": "0.80.2", "@react-native/metro-config": "0.80.2", "@react-native/typescript-config": "0.80.2", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}