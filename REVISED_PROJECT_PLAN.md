# REVISED Food Scanning App - Project Plan

## CRITICAL LEARNINGS FROM EXPO ATTEMPT

**❌ What Didn't Work:**
- Expo Camera has broken/limited web support
- Cross-platform camera implementations are unreliable in Expo
- Complex bundling issues prevent basic functionality
- "Write once, run everywhere" is mostly marketing for camera apps

**✅ Industry Reality:**
- 99% of successful mobile apps use separate implementations
- React Native CLI for mobile + separate web app is standard
- Different platforms need different UX patterns anyway

## NEW RECOMMENDED ARCHITECTURE

### Option 1: Separate Apps (Recommended)
```
FoodApp/
├── mobile/ (React Native CLI)
│   ├── iOS & Android only
│   ├── Native camera integration
│   ├── Real-time food scanning
│   └── App Store deployment
└── web/ (Next.js/Vite + React)
    ├── Web-optimized UI
    ├── File upload for photos
    ├── Desktop-friendly interface
    └── Shared backend APIs
```

### Option 2: Flutter Alternative
```
food_scanner/
├── Single Flutter codebase
├── Excellent camera support all platforms
├── Consistent performance
└── Easier maintenance
```

## PRESERVED ASSETS FROM CURRENT PROJECT

### 1. Google Vision Integration (Ready to Use)
- ✅ **Complete AI service implementation** in `/services/aiService.ts`
- ✅ **Mock data for development** (no API key needed)
- ✅ **Production-ready** Google Vision API integration
- ✅ **Environment variable setup** in `app.json`
- ✅ **Cost-effective**: $1.50/1000 requests, 1000 free/month

### 2. App Architecture & Requirements
- ✅ **Brutalist design system** with theme constants
- ✅ **Canadian market focus** (metric, bilingual)
- ✅ **Database strategy** (OpenFoodFacts + CNF API)
- ✅ **State management** (Zustand planned)
- ✅ **Performance targets** (<5 second recognition)

### 3. UI Components (Salvageable)
- ✅ **BrutalistButton, BrutalistText, BrutalistView** components
- ✅ **Theme constants** with proper color palette
- ✅ **Navigation structure** (Dashboard, Capture, Search, Profile)

## IMPLEMENTATION PLAN FOR NEXT AGENT

### Phase 1: Choose Platform & Setup (1-2 hours)
**Decision Point:** React Native CLI + Web App OR Flutter

**If React Native CLI:**
```bash
# Mobile App
npx react-native init FoodScannerMobile --template typescript
cd FoodScannerMobile
npm install react-native-vision-camera react-native-permissions

# Web App  
npm create next-app@latest food-scanner-web --typescript --tailwind
```

**If Flutter:**
```bash
flutter create food_scanner
cd food_scanner
flutter pub add camera permission_handler
```

### Phase 2: Port Core Components (2-3 hours)
1. **Copy Google Vision service** from current `/services/aiService.ts`
2. **Port brutalist UI components** to new platform
3. **Implement native camera** (React Native Vision Camera OR Flutter camera)
4. **Setup state management** (Zustand/Redux OR Provider/Riverpod)

### Phase 3: Core MVP (4-6 hours)
1. **Camera screen** with real native camera
2. **Photo capture** → Google Vision API → Food results
3. **Basic food logging** with calories/macros
4. **Simple dashboard** showing daily totals

### Phase 4: Polish & Deploy (2-3 hours)
1. **Error handling** and offline support
2. **Platform-specific optimizations**
3. **Deploy** to app stores/web hosting

## PRESERVED BUSINESS LOGIC

### Food Recognition Flow (Working Code Available)
```typescript
// From current /services/aiService.ts - READY TO USE
1. takePicture() → captures image
2. aiService.recognizeFood(imageUri) → Google Vision API
3. Returns: { confidence: number, foods: FoodItem[] }
4. User selects/confirms food items
5. Logs to daily tracking
```

### Data Models (Already Defined)
```typescript
// From current /store/types.ts
interface FoodItem {
  id: string;
  name: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  serving_size: string;
}

interface LoggedFood extends FoodItem {
  quantity: number;
  meal_type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  logged_at: string;
}
```

## SUCCESS METRICS (Unchanged)
- **AI accuracy**: >85% for common foods
- **Recognition speed**: <5 seconds total
- **User retention**: 40% after 30 days
- **App store rating**: 4.5+

## NEXT AGENT INSTRUCTIONS

1. **Review this plan** and choose platform approach
2. **Salvage working code** from current `/services/` and `/components/`
3. **Implement proper native camera** (not Expo Camera)
4. **Focus on MVP first** - get food scanning working reliably
5. **Use Canadian food database** (OpenFoodFacts) as specified in guide.md

## KEY LESSON LEARNED
**Camera apps need native implementations.** Cross-platform tools like Expo sacrifice camera reliability for convenience. Industry standard is separate mobile/web apps for good reason.

The Google Vision integration and business logic are solid - just need proper native camera implementation to make this work.